import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// GeekAssistant API接口
interface GeekAssistantAPI {
  // 窗口管理
  createFloatingWindow: () => Promise<boolean>
  closeFloatingWindow: () => Promise<boolean>
  enterCollaborationMode: () => Promise<boolean>
  exitCollaborationMode: () => Promise<boolean>

  // 服务管理
  initializeServices: () => Promise<boolean>
  switchService: (config: any) => Promise<boolean>
  startSession: () => Promise<boolean>
  stopSession: () => Promise<boolean>
  updateTranscriptionLanguage: (language: string) => Promise<boolean>

  // 音频管理
  startAudioCapture: () => Promise<boolean>
  stopAudioCapture: () => Promise<boolean>

  // 传统Gemini API（向后兼容）
  initializeGemini: (apiKey: string, customPrompt?: string, profile?: string, language?: string) => Promise<boolean>
  reconnectGemini: () => Promise<boolean>
  manualReconnect: () => Promise<boolean>
  disconnectGemini: () => Promise<boolean>

  // 权限管理
  checkPermissions: () => Promise<any>
  checkScreenRecordingPermission: () => Promise<any>
  checkMicrophonePermission: () => Promise<any>
  checkApiKeyStatus: () => Promise<any>
  checkAudioDeviceStatus: () => Promise<any>
  openSystemPreferences: (pane: string) => Promise<boolean>
  testAudioCapture: () => Promise<any>
  requestMicrophonePermission: () => Promise<any>

  // 配置管理
  getCurrentServiceConfig: () => Promise<any>
  updateServiceConfig: (config: any) => Promise<boolean>
  reloadServiceConfig: () => Promise<any>

  // 自定义API管理
  customAPI?: {
    getAll: () => Promise<any[]>
    add: (api: any) => Promise<boolean>
    update: (id: string, updates: any) => Promise<boolean>
    remove: (id: string) => Promise<boolean>
    testChat: (id: string, message: string) => Promise<any>
  }

  // 事件监听
  onStatusUpdate: (callback: (status: string) => void) => () => void
  onTranscriptionUpdate: (callback: (text: string) => void) => () => void
  onAIResponse: (callback: (response: any) => void) => () => void
  onSessionError: (callback: (error: string) => void) => () => void
  onSessionClosed: (callback: () => void) => () => void
  onConfigUpdated: (callback: (config: any) => void) => () => void
}

// 暴露给渲染进程的API
const geekAssistantAPI: GeekAssistantAPI = {
  // 窗口管理
  createFloatingWindow: () => ipcRenderer.invoke('create-floating-window'),
  closeFloatingWindow: () => ipcRenderer.invoke('close-floating-window'),
  enterCollaborationMode: () => ipcRenderer.invoke('enter-collaboration-mode'),
  exitCollaborationMode: () => ipcRenderer.invoke('exit-collaboration-mode'),

  // 服务管理
  initializeServices: () => ipcRenderer.invoke('initialize-services'),
  switchService: (config: any) => ipcRenderer.invoke('switch-service', config),
  startSession: () => ipcRenderer.invoke('start-session'),
  stopSession: () => ipcRenderer.invoke('stop-session'),
  updateTranscriptionLanguage: (language: string) => ipcRenderer.invoke('update-transcription-language', language),

  // 音频管理
  startAudioCapture: () => ipcRenderer.invoke('start-audio-capture'),
  stopAudioCapture: () => ipcRenderer.invoke('stop-audio-capture'),

  // 配置管理
  getCurrentServiceConfig: () => ipcRenderer.invoke('get-current-service-config'),
  updateServiceConfig: (config: any) => ipcRenderer.invoke('update-service-config', config),
  reloadServiceConfig: () => ipcRenderer.invoke('reload-service-config'),

  // 自定义API管理
  customAPI: {
    getAll: () => ipcRenderer.invoke('custom-api-get-all'),
    add: (api: any) => ipcRenderer.invoke('custom-api-add', api),
    update: (id: string, updates: any) => ipcRenderer.invoke('custom-api-update', id, updates),
    remove: (id: string) => ipcRenderer.invoke('custom-api-remove', id),
    testChat: (id: string, message: string) => ipcRenderer.invoke('custom-api-test-chat', id, message)
  },

  // 传统Gemini API（向后兼容）
  initializeGemini: (apiKey, customPrompt = '', profile = 'interview', language = 'cmn-CN') =>
    ipcRenderer.invoke('initialize-gemini', apiKey, customPrompt, profile, language),

  // Gemini 连接管理
  reconnectGemini: () => ipcRenderer.invoke('reconnect-gemini'),
  manualReconnect: () => ipcRenderer.invoke('manual-reconnect'),
  disconnectGemini: () => ipcRenderer.invoke('disconnect-gemini'),

  // 权限管理
  checkPermissions: () => ipcRenderer.invoke('check-permissions'),
  checkScreenRecordingPermission: () => ipcRenderer.invoke('check-screen-recording-permission'),
  checkMicrophonePermission: () => ipcRenderer.invoke('check-microphone-permission'),
  checkApiKeyStatus: () => ipcRenderer.invoke('check-api-key-status'),
  checkAudioDeviceStatus: () => ipcRenderer.invoke('check-audio-device-status'),
  openSystemPreferences: (pane: string) => ipcRenderer.invoke('open-system-preferences', pane),
  testAudioCapture: () => ipcRenderer.invoke('test-audio-capture'),
  requestMicrophonePermission: () => ipcRenderer.invoke('request-microphone-permission'),
  
  // 事件监听
  onStatusUpdate: (callback) => {
    const listener = (_: any, status: string) => callback(status)
    ipcRenderer.on('update-status', listener)
    return () => ipcRenderer.removeListener('update-status', listener)
  },
  
  onTranscriptionUpdate: (callback) => {
    const listener = (_: any, text: string) => callback(text)
    ipcRenderer.on('transcription-update', listener)
    return () => ipcRenderer.removeListener('transcription-update', listener)
  },
  
  onAIResponse: (callback) => {
    const listener = (_: any, response: any) => callback(response)
    ipcRenderer.on('ai-response', listener)
    return () => ipcRenderer.removeListener('ai-response', listener)
  },
  
  onSessionInitializing: (callback) => {
    const listener = (_: any, initializing: boolean) => callback(initializing)
    ipcRenderer.on('session-initializing', listener)
    return () => ipcRenderer.removeListener('session-initializing', listener)
  },
  
  onSessionError: (callback) => {
    const listener = (_: any, error: string) => callback(error)
    ipcRenderer.on('session-error', listener)
    return () => ipcRenderer.removeListener('session-error', listener)
  },
  
  onSessionClosed: (callback) => {
    const listener = () => callback()
    ipcRenderer.on('session-closed', listener)
    return () => ipcRenderer.removeListener('session-closed', listener)
  },

  onConfigUpdated: (callback) => {
    const listener = (_: any, config: any) => callback(config)
    ipcRenderer.on('config-updated', listener)
    return () => ipcRenderer.removeListener('config-updated', listener)
  }
}

// 使用contextBridge暴露API
contextBridge.exposeInMainWorld('geekAssistant', geekAssistantAPI)

// 也可以暴露Node.js环境变量
contextBridge.exposeInMainWorld('env', {
  GEMINI_API_KEY: process.env.VITE_GEMINI_API_KEY,
  SUPABASE_URL: process.env.VITE_SUPABASE_URL,
  SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY,
  DEV_MODE: process.env.VITE_DEV_MODE
})
