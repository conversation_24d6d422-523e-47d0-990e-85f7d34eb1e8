/**
 * Azure转录服务测试脚本
 * 用于验证Azure账号管理器和转录服务的基本功能
 */

import { AzureAccountManager } from './services/AzureAccountManager';
import { AzureTranscriptionService } from './services/AzureTranscriptionService';

async function testAzureAccountManager() {
  console.log('🧪 Testing Azure Account Manager...');
  
  const accountManager = new AzureAccountManager();
  
  try {
    // 初始化账号管理器
    const initialized = await accountManager.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize account manager');
    }
    
    console.log('✅ Account manager initialized successfully');
    
    // 获取当前账号
    const currentAccount = accountManager.getCurrentAccount();
    if (currentAccount) {
      console.log(`✅ Current account: ${currentAccount.name}`);
      console.log(`✅ Remaining minutes: ${accountManager.getCurrentAccountRemainingMinutes()}`);
      console.log(`✅ Total remaining minutes: ${accountManager.getTotalRemainingMinutes()}`);
      console.log(`✅ Active accounts: ${accountManager.getActiveAccounts().length}`);
    } else {
      console.log('❌ No current account available');
    }
    
    // 测试账号切换
    console.log('🔄 Testing account switching...');
    const switched = await accountManager.switchAccount();
    if (switched) {
      const newAccount = accountManager.getCurrentAccount();
      console.log(`✅ Switched to account: ${newAccount?.name}`);
    } else {
      console.log('❌ Failed to switch account');
    }
    
    // 清理
    await accountManager.cleanup();
    console.log('✅ Account manager cleaned up');
    
  } catch (error) {
    console.error('❌ Account manager test failed:', error);
  }
}

async function testAzureTranscriptionService() {
  console.log('🧪 Testing Azure Transcription Service...');
  
  const config = {
    language: 'zh-CN',
    realtime: true,
    punctuate: true,
    recognitionMode: 'conversation' as const,
    profanityOption: 'masked' as const,
    outputFormat: 'simple' as const
  };
  
  const transcriptionService = new AzureTranscriptionService(config);
  
  try {
    // 初始化服务
    const initialized = await transcriptionService.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize transcription service');
    }
    
    console.log('✅ Transcription service initialized successfully');
    
    // 获取账号状态
    const accountStatus = transcriptionService.getAccountStatus();
    console.log('✅ Account status:', accountStatus);
    
    // 测试服务状态
    console.log(`✅ Service status: ${transcriptionService.getStatus()}`);
    
    // 设置事件监听器
    transcriptionService.onTranscription((result) => {
      console.log('📝 Transcription result:', result);
    });
    
    transcriptionService.onError((error) => {
      console.error('❌ Transcription error:', error);
    });
    
    transcriptionService.onStatusChange((status) => {
      console.log(`🔄 Status changed: ${status}`);
    });
    
    // 注意：这里不启动实际的转录，因为需要音频输入
    console.log('✅ Event listeners set up successfully');
    
    // 清理
    transcriptionService.cleanup();
    console.log('✅ Transcription service cleaned up');
    
  } catch (error) {
    console.error('❌ Transcription service test failed:', error);
  }
}

async function main() {
  console.log('🚀 Starting Azure integration tests...\n');
  
  try {
    await testAzureAccountManager();
    console.log('\n' + '='.repeat(50) + '\n');
    await testAzureTranscriptionService();
    
    console.log('\n🎉 All tests completed!');
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { testAzureAccountManager, testAzureTranscriptionService };
