import { EventEmitter } from 'events';
import { BrowserWindow } from 'electron';
import { ServiceConfig } from './ServiceTypes';

export enum AdapterStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

export interface StatusInfo {
  status: AdapterStatus;
  lastActivity: number;
  errorMessage?: string;
}

/**
 * 服务适配器基类
 * 所有具体的服务适配器都继承自这个基类
 */
export abstract class ServiceAdapter extends EventEmitter {
  protected config: ServiceConfig;
  protected isInitialized = false;
  protected statusInfo: StatusInfo;

  constructor(config: ServiceConfig) {
    super();
    this.config = config;
    this.statusInfo = {
      status: AdapterStatus.DISCONNECTED,
      lastActivity: Date.now()
    };
  }

  /**
   * 获取服务状态信息
   */
  getStatus(): StatusInfo {
    return { ...this.statusInfo };
  }

  /**
   * 获取配置
   */
  getConfig(): ServiceConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  async updateConfig(config: ServiceConfig): Promise<boolean> {
    this.config = { ...this.config, ...config };
    return true;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.statusInfo.status === AdapterStatus.CONNECTED;
  }

  /**
   * 发送事件到前端
   */
  emitServiceEvent(event: string, data: any): void {
    const eventData = {
      event,
      data,
      timestamp: Date.now(),
      source: this.constructor.name
    };
    
    this.emit('service-event', eventData);
    this.sendToRenderer(event, data);
  }

  /**
   * 更新服务状态
   */
  protected updateStatus(status: AdapterStatus, errorMessage?: string): void {
    this.statusInfo = {
      ...this.statusInfo,
      status,
      errorMessage,
      lastActivity: Date.now()
    };
    
    this.emitServiceEvent('status-changed', this.statusInfo);
  }

  /**
   * 处理转录结果
   */
  protected handleTranscription(result: any): void {
    this.emitServiceEvent('transcription-received', result);
  }

  /**
   * 处理AI响应
   */
  protected handleAIResponse(response: any): void {
    this.emitServiceEvent('ai-response-received', response);
  }

  /**
   * 处理错误
   */
  protected handleError(error: any): void {
    const errorMessage = typeof error === 'string' ? error : error.message;
    this.updateStatus(AdapterStatus.ERROR, errorMessage);
    this.emitServiceEvent('error-occurred', { error: errorMessage });
  }

  /**
   * 发送消息到渲染器进程
   */
  protected sendToRenderer(event: string, data?: any): void {
    try {
      const windows = BrowserWindow.getAllWindows();
      if (windows.length === 0) return;

      windows.forEach((window) => {
        if (window && !window.isDestroyed()) {
          window.webContents.send(event, data);
        }
      });
    } catch (error) {
      // 忽略错误，避免循环错误
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.removeAllListeners();
  }

  /**
   * 更新转录语言（可选实现）
   */
  async updateTranscriptionLanguage(language: string): Promise<void> {
    console.log('🌍 ServiceAdapter: Updating transcription language to:', language);
    this.config.transcription.language = language;
    // 子类可以重写此方法来实现具体的语言更新逻辑
  }

  // 抽象方法，子类必须实现
  abstract initialize(config?: ServiceConfig): Promise<boolean>;
  abstract startSession(config?: any): Promise<boolean>;
  abstract stopSession(): Promise<void>;
  abstract sendAudio(audioData: Buffer): Promise<void>;
  abstract reconnect(): Promise<boolean>;
  abstract disconnect(): Promise<void>;
}
