import { promises as fs } from 'fs';
import { join } from 'path';
import { app } from 'electron';

export interface CustomAPI {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  apiKey: string;
  model: string;
  headers?: Record<string, string>;
  requestFormat?: 'openai' | 'custom';
  responseFormat?: 'openai' | 'custom';
  customRequestTemplate?: string;
  customResponseParser?: string;
  createdAt: number;
  updatedAt: number;
}

export interface CustomAPIStore {
  version: string;
  apis: Record<string, CustomAPI>;
  lastUpdated: number;
}

/**
 * 自定义API管理器
 * 管理用户自定义的AI API配置
 */
export class CustomAPIManager {
  private static instance: CustomAPIManager | null = null;
  private store: CustomAPIStore;
  private storePath: string;
  private isInitialized = false;

  constructor() {
    this.storePath = join(app.getPath('userData'), 'custom-apis.json');
    this.store = {
      version: '1.0.0',
      apis: {},
      lastUpdated: Date.now()
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): CustomAPIManager {
    if (!CustomAPIManager.instance) {
      CustomAPIManager.instance = new CustomAPIManager();
    }
    return CustomAPIManager.instance;
  }

  /**
   * 初始化管理器
   */
  async initialize(): Promise<boolean> {
    try {
      await this.loadStore();
      this.isInitialized = true;
      console.log('🎨 CustomAPIManager initialized successfully');
      return true;
    } catch (error) {
      console.error('🎨❌ Failed to initialize CustomAPIManager:', error);
      return false;
    }
  }

  /**
   * 添加自定义API
   */
  async addAPI(apiData: Omit<CustomAPI, 'id' | 'createdAt' | 'updatedAt'>): Promise<CustomAPI | null> {
    try {
      const api: CustomAPI = {
        ...apiData,
        id: this.generateId(),
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      this.store.apis[api.id] = api;
      this.store.lastUpdated = Date.now();
      
      await this.saveStore();
      console.log('🎨✅ Custom API added:', api.name);
      return api;
    } catch (error) {
      console.error('🎨❌ Failed to add custom API:', error);
      return null;
    }
  }

  /**
   * 更新自定义API
   */
  async updateAPI(id: string, updates: Partial<Omit<CustomAPI, 'id' | 'createdAt'>>): Promise<boolean> {
    try {
      if (!this.store.apis[id]) {
        console.error('🎨❌ Custom API not found:', id);
        return false;
      }

      this.store.apis[id] = {
        ...this.store.apis[id],
        ...updates,
        updatedAt: Date.now()
      };
      
      this.store.lastUpdated = Date.now();
      await this.saveStore();
      
      console.log('🎨✅ Custom API updated:', id);
      return true;
    } catch (error) {
      console.error('🎨❌ Failed to update custom API:', error);
      return false;
    }
  }

  /**
   * 删除自定义API
   */
  async removeAPI(id: string): Promise<boolean> {
    try {
      if (!this.store.apis[id]) {
        console.error('🎨❌ Custom API not found:', id);
        return false;
      }

      delete this.store.apis[id];
      this.store.lastUpdated = Date.now();
      
      await this.saveStore();
      console.log('🎨✅ Custom API removed:', id);
      return true;
    } catch (error) {
      console.error('🎨❌ Failed to remove custom API:', error);
      return false;
    }
  }

  /**
   * 获取所有自定义API
   */
  getAllAPIs(): CustomAPI[] {
    return Object.values(this.store.apis);
  }

  /**
   * 根据ID获取自定义API
   */
  getAPI(id: string): CustomAPI | null {
    return this.store.apis[id] || null;
  }

  /**
   * 测试API连接
   */
  async testAPI(id: string): Promise<{ success: boolean; message: string; latency?: number }> {
    try {
      const api = this.getAPI(id);
      if (!api) {
        return { success: false, message: 'API配置不存在' };
      }

      const startTime = Date.now();
      
      // 构建测试请求
      const testMessage = '你好，这是一个连接测试。';
      const response = await this.makeAPIRequest(api, testMessage);
      
      const latency = Date.now() - startTime;

      if (response.success) {
        return {
          success: true,
          message: '连接成功',
          latency
        };
      } else {
        return {
          success: false,
          message: response.error || '连接失败'
        };
      }
    } catch (error: any) {
      console.error('🎨❌ API test failed:', error);
      return {
        success: false,
        message: error.message || '测试失败'
      };
    }
  }

  /**
   * 测试API对话功能
   */
  async testAPIChat(id: string, message: string): Promise<{ success: boolean; response?: string; message: string }> {
    try {
      const api = this.getAPI(id);
      if (!api) {
        return { success: false, message: 'API配置不存在' };
      }

      const result = await this.makeAPIRequest(api, message);
      
      if (result.success) {
        return {
          success: true,
          response: result.response,
          message: '对话测试成功'
        };
      } else {
        return {
          success: false,
          message: result.error || '对话测试失败'
        };
      }
    } catch (error: any) {
      console.error('🎨❌ API chat test failed:', error);
      return {
        success: false,
        message: error.message || '对话测试失败'
      };
    }
  }

  /**
   * 从URL导入API配置
   */
  async importFromURL(url: string): Promise<CustomAPI | null> {
    try {
      // 这里可以实现从URL导入API配置的逻辑
      // 例如从OpenAPI规范或自定义配置URL导入
      console.log('🎨 Importing API from URL:', url);
      
      // 暂时返回null，实际实现需要根据具体需求
      return null;
    } catch (error) {
      console.error('🎨❌ Failed to import API from URL:', error);
      return null;
    }
  }

  /**
   * 导出API配置为URL
   */
  exportToURL(id: string): string | null {
    try {
      const api = this.getAPI(id);
      if (!api) {
        return null;
      }

      // 生成配置URL（可以是base64编码的配置）
      const config = {
        name: api.name,
        baseUrl: api.baseUrl,
        model: api.model,
        requestFormat: api.requestFormat,
        responseFormat: api.responseFormat
      };

      const encodedConfig = Buffer.from(JSON.stringify(config)).toString('base64');
      return `geekassistant://import-api?config=${encodedConfig}`;
    } catch (error) {
      console.error('🎨❌ Failed to export API to URL:', error);
      return null;
    }
  }

  /**
   * 发起API请求
   */
  private async makeAPIRequest(api: CustomAPI, message: string): Promise<{ success: boolean; response?: string; error?: string }> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${api.apiKey}`,
        ...api.headers
      };

      let requestBody: any;

      if (api.requestFormat === 'openai' || !api.requestFormat) {
        // OpenAI格式
        requestBody = {
          model: api.model,
          messages: [
            { role: 'user', content: message }
          ],
          max_tokens: 100,
          temperature: 0.7
        };
      } else if (api.customRequestTemplate) {
        // 自定义格式
        requestBody = JSON.parse(
          api.customRequestTemplate
            .replace('{{message}}', message)
            .replace('{{model}}', api.model)
        );
      }

      const response = await fetch(api.baseUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      let responseText: string;

      if (api.responseFormat === 'openai' || !api.responseFormat) {
        // OpenAI格式响应解析
        responseText = data.choices?.[0]?.message?.content || data.choices?.[0]?.text || '无响应内容';
      } else if (api.customResponseParser) {
        // 自定义响应解析
        try {
          const parser = new Function('data', api.customResponseParser);
          responseText = parser(data) || '解析失败';
        } catch (error) {
          responseText = '响应解析错误';
        }
      } else {
        responseText = JSON.stringify(data);
      }

      return {
        success: true,
        response: responseText
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 加载存储数据
   */
  private async loadStore(): Promise<void> {
    try {
      const data = await fs.readFile(this.storePath, 'utf-8');
      this.store = JSON.parse(data);
      console.log('🎨 Custom API store loaded successfully');
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        console.log('🎨 Custom API store not found, creating new one');
        await this.saveStore();
      } else {
        throw error;
      }
    }
  }

  /**
   * 保存存储数据
   */
  private async saveStore(): Promise<void> {
    try {
      const data = JSON.stringify(this.store, null, 2);
      await fs.writeFile(this.storePath, data, 'utf-8');
      console.log('🎨 Custom API store saved successfully');
    } catch (error) {
      console.error('🎨❌ Failed to save custom API store:', error);
      throw error;
    }
  }
}
