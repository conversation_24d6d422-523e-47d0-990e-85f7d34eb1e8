import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { createClient, LiveTranscriptionEvents } from '@deepgram/sdk';

/**
 * Deepgram特定配置接口
 */
export interface DeepgramConfig extends TranscriptionConfig {
  model?: string;
  smartFormat?: boolean;
  punctuate?: boolean;
  diarize?: boolean;
  multichannel?: boolean;
  alternatives?: number;
  numerals?: boolean;
  search?: string[];
  replace?: string[];
  keywords?: string[];
  interim_results?: boolean;
  endpointing?: number;
  vad_turnoff?: number;
  encoding?: string;
  channels?: number;
  sample_rate?: number;
}

/**
 * Deepgram语音转录服务 - 使用官方SDK
 * 基于Deepgram官方JavaScript SDK
 * 支持实时流式转录和高级语音识别功能
 */
export class DeepgramTranscriptionService extends BaseTranscriptionService {
  private config: DeepgramConfig;
  private isTranscribing = false;
  private deepgram: any = null;
  private connection: any = null;

  constructor(config: DeepgramConfig) {
    super(config);
    this.config = {
      realtime: true,
      punctuate: true,
      timeout: 10000,
      retryAttempts: 2,
      model: 'nova-2',
      smartFormat: true,
      interim_results: true,
      encoding: 'linear16',
      channels: 1,
      sample_rate: 16000,
      ...config
    };
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing Deepgram transcription service...');
      
      if (!this.config.apiKey) {
        throw new Error('Deepgram API key is required');
      }

      // 创建Deepgram客户端
      this.deepgram = createClient(this.config.apiKey);
      
      console.log('🎤✅ Deepgram transcription service initialized');
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to initialize Deepgram transcription service:', error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    if (this.isTranscribing) {
      console.log('🎤⚠️ Deepgram transcription already running');
      return true;
    }

    if (!this.deepgram) {
      throw new Error('Deepgram client not initialized');
    }

    try {
      console.log('🎤 Starting Deepgram transcription...');
      
      // 创建实时转录连接 - 过滤undefined参数
      const connectionOptions: any = {
        model: this.config.model,
        language: this.config.language || 'zh',
        smart_format: this.config.smartFormat,
        punctuate: this.config.punctuate,
        interim_results: this.config.interim_results,
        encoding: this.config.encoding,
        channels: this.config.channels,
        sample_rate: this.config.sample_rate
      };

      // 只添加非undefined的可选参数
      if (this.config.diarize !== undefined) connectionOptions.diarize = this.config.diarize;
      if (this.config.multichannel !== undefined) connectionOptions.multichannel = this.config.multichannel;
      if (this.config.alternatives !== undefined) connectionOptions.alternatives = this.config.alternatives;
      if (this.config.numerals !== undefined) connectionOptions.numerals = this.config.numerals;
      if (this.config.search !== undefined) connectionOptions.search = this.config.search;
      if (this.config.replace !== undefined) connectionOptions.replace = this.config.replace;
      if (this.config.keywords !== undefined) connectionOptions.keywords = this.config.keywords;
      if (this.config.endpointing !== undefined) connectionOptions.endpointing = this.config.endpointing;
      if (this.config.vad_turnoff !== undefined) connectionOptions.vad_turnoff = this.config.vad_turnoff;

      this.connection = this.deepgram.listen.live(connectionOptions);

      // 设置事件监听器
      this.setupConnectionEvents();
      
      this.isTranscribing = true;
      console.log('🎤✅ Deepgram transcription started');
      this.emit('status-changed', TranscriptionStatus.CONNECTED);
      
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to start Deepgram transcription:', error);
      this.isTranscribing = false;
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('🎤 Stopping Deepgram transcription...');
    
    this.isTranscribing = false;
    
    if (this.connection) {
      try {
        this.connection.finish();
        this.connection = null;
      } catch (error) {
        console.error('🎤❌ Error stopping Deepgram connection:', error);
      }
    }
    
    console.log('🎤✅ Deepgram transcription stopped');
    this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.isTranscribing || !this.connection) {
      return;
    }

    try {
      // Deepgram期望Buffer格式的音频数据
      this.connection.send(audioData);
    } catch (error) {
      console.error('🎤❌ Failed to send audio to Deepgram:', error);
      this.handleError(error);
    }
  }

  /**
   * 设置连接事件监听
   */
  private setupConnectionEvents(): void {
    if (!this.connection) return;

    // 连接打开
    this.connection.on(LiveTranscriptionEvents.Open, () => {
      console.log('🎤🎬 Deepgram connection opened');
    });

    // 连接关闭
    this.connection.on(LiveTranscriptionEvents.Close, () => {
      console.log('🎤🛑 Deepgram connection closed');
      this.isTranscribing = false;
      this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
    });

    // 转录结果
    this.connection.on(LiveTranscriptionEvents.Transcript, (data: any) => {
      try {
        const transcript = data.channel?.alternatives?.[0]?.transcript;
        if (transcript && transcript.trim()) {
          const result: TranscriptionResult = {
            text: transcript,
            isFinal: data.is_final || false,
            confidence: data.channel?.alternatives?.[0]?.confidence || 0.8,
            timestamp: Date.now(),
            language: this.config.language
          };

          console.log(`🎤${result.isFinal ? '✅' : '📝'} Deepgram result: ${result.text}`);
          this.emit('transcription', result);
        }
      } catch (error) {
        console.error('🎤❌ Error processing Deepgram transcript:', error);
      }
    });

    // 元数据
    this.connection.on(LiveTranscriptionEvents.Metadata, (data: any) => {
      console.log('🎤📊 Deepgram metadata:', data);
    });

    // 错误处理
    this.connection.on(LiveTranscriptionEvents.Error, (error: any) => {
      console.error('🎤❌ Deepgram error:', error);
      this.handleError(error);
    });
  }

  /**
   * 处理错误
   */
  private async handleError(error: any): Promise<void> {
    console.error('🎤❌ Deepgram transcription error:', error);
    
    const errorMessage = error.message || error.toString();
    
    // 检查是否是认证错误
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('authentication')) {
      console.log('🎤🔑 Authentication error detected');
      this.emit('error', new Error('Deepgram authentication failed. Please check your API key.'));
    } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      console.log('🎤⏰ Rate limit error detected');
      this.emit('error', new Error('Deepgram rate limit exceeded. Please try again later.'));
    } else {
      this.emit('error', error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    if (this.isTranscribing && this.connection) {
      return TranscriptionStatus.CONNECTED;
    }
    return TranscriptionStatus.DISCONNECTED;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopTranscription();
    this.deepgram = null;
  }
}
