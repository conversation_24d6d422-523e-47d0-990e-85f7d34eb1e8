import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { AzureAccountManager, AzureAccount } from './AzureAccountManager';
import * as sdk from 'microsoft-cognitiveservices-speech-sdk';

/**
 * Azure特定配置接口
 */
export interface AzureConfig extends TranscriptionConfig {
  region?: string;
  recognitionMode?: 'conversation' | 'dictation';
  profanityOption?: 'masked' | 'removed' | 'raw';
  outputFormat?: 'simple' | 'detailed';
  punctuate?: boolean;
  sampleRate?: number;
  channels?: number;
}

/**
 * Azure语音转录服务 - 使用微软官方Speech SDK
 * 基于Microsoft Cognitive Services Speech SDK
 * 支持多账号自动切换和使用时长管理
 */
export class AzureTranscriptionService extends BaseTranscriptionService {
  private config: AzureConfig;
  private isTranscribing = false;
  private accountManager: AzureAccountManager;
  private currentAccount: AzureAccount | null = null;
  private speechConfig: sdk.SpeechConfig | null = null;
  private audioConfig: sdk.AudioConfig | null = null;
  private recognizer: sdk.SpeechRecognizer | null = null;
  private pushStream: sdk.PushAudioInputStream | null = null;

  constructor(config: AzureConfig) {
    super(config);
    this.config = {
      realtime: true,
      punctuate: true,
      timeout: 10000,
      retryAttempts: 2,
      sampleRate: 16000,
      channels: 1,
      recognitionMode: 'conversation',
      profanityOption: 'masked',
      outputFormat: 'simple',
      ...config
    };

    this.accountManager = new AzureAccountManager();
    this.setupAccountManager();
  }

  /**
   * 设置账号管理器事件监听
   */
  private setupAccountManager(): void {
    this.accountManager.on('account-switched', (account: AzureAccount) => {
      console.log(`🔑 Account switched to: ${account.name}`);
      this.currentAccount = account;
    });

    this.accountManager.on('account-expired', (accountId: string) => {
      console.log(`🔑⏰ Account expired: ${accountId}`);
      this.switchAccount();
    });

    this.accountManager.on('no-accounts-available', () => {
      console.error('🔑❌ No Azure accounts available');
      this.emit('error', new Error('No Azure accounts available'));
    });
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing Azure transcription service...');

      await this.accountManager.initialize();
      this.currentAccount = this.accountManager.getCurrentAccount();

      // 如果没有可用账号或当前账号token过期，自动刷新
      if (!this.currentAccount || !this.isTokenValid(this.currentAccount)) {
        console.log('🎤🔄 No valid account found, attempting to refresh tokens...');

        const accounts = this.accountManager.getAllAccounts();
        let refreshSuccess = false;

        for (const account of accounts) {
          console.log(`🎤🔄 Attempting to refresh token for: ${account.name}`);
          const refreshed = await this.accountManager.refreshAccountToken(account.id);

          if (refreshed) {
            console.log(`🎤✅ Token refreshed successfully for: ${account.name}`);
            this.currentAccount = this.accountManager.getCurrentAccount();
            refreshSuccess = true;
            break;
          } else {
            console.log(`🎤⚠️ Token refresh failed for: ${account.name}`);
          }
        }

        if (!refreshSuccess) {
          throw new Error('Failed to refresh any account tokens');
        }
      }

      console.log(`🎤✅ Azure transcription service initialized with account: ${this.currentAccount.name}`);
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to initialize Azure transcription service:', error);
      return false;
    }
  }

  /**
   * 检查token是否有效
   */
  private isTokenValid(account: AzureAccount): boolean {
    if (!account.azureCredentials?.token) {
      return false;
    }

    try {
      const tokenParts = account.azureCredentials.token.split('.');
      if (tokenParts.length !== 3) {
        return false;
      }

      const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
      const expTime = new Date(payload.exp * 1000);
      const now = new Date();

      // 提前5分钟判断过期
      return (expTime.getTime() - now.getTime()) > 5 * 60 * 1000;
    } catch (error) {
      console.error('🎤❌ Failed to validate token:', error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    if (this.isTranscribing) {
      console.log('🎤⚠️ Azure transcription already running');
      return;
    }

    if (!this.currentAccount) {
      throw new Error('No current Azure account available');
    }

    try {
      console.log('🎤 Starting Azure transcription...');
      
      // 创建Speech配置
      this.speechConfig = sdk.SpeechConfig.fromAuthorizationToken(
        this.currentAccount.azureCredentials.token,
        this.currentAccount.azureCredentials.region
      );
      
      this.speechConfig.speechRecognitionLanguage = this.config.language || 'zh-CN';
      this.speechConfig.outputFormat = sdk.OutputFormat.Simple;
      
      // 创建音频流 - 使用正确的PCM格式
      const audioFormat = sdk.AudioStreamFormat.getWaveFormatPCM(
        this.config.sampleRate || 16000,
        16, // 16-bit
        this.config.channels || 1
      );

      this.pushStream = sdk.AudioInputStream.createPushStream(audioFormat);
      
      this.audioConfig = sdk.AudioConfig.fromStreamInput(this.pushStream);
      
      // 创建识别器
      this.recognizer = new sdk.SpeechRecognizer(this.speechConfig, this.audioConfig);
      
      // 设置事件监听器
      this.setupRecognizerEvents();
      
      // 开始连续识别
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          console.log('🎤✅ Azure transcription started');
          this.isTranscribing = true;
          this.emit('status-changed', TranscriptionStatus.CONNECTED);
        },
        (error) => {
          console.error('🎤❌ Failed to start Azure transcription:', error);
          this.handleError(error);
          return false;
        }
      );

      return true;
    } catch (error) {
      console.error('🎤❌ Failed to start Azure transcription:', error);
      this.isTranscribing = false;
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('🎤 Stopping Azure transcription...');
    
    this.isTranscribing = false;
    
    if (this.recognizer) {
      this.recognizer.stopContinuousRecognitionAsync(
        () => {
          console.log('🎤✅ Azure transcription stopped');
          this.cleanup();
          this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
        },
        (error) => {
          console.error('🎤❌ Error stopping transcription:', error);
          this.cleanup();
        }
      );
    } else {
      this.cleanup();
      this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.isTranscribing || !this.pushStream) {
      return;
    }

    try {
      // 将Node.js Buffer转换为ArrayBuffer
      const arrayBuffer = audioData.buffer.slice(
        audioData.byteOffset,
        audioData.byteOffset + audioData.byteLength
      );

      // 将音频数据推送到流中
      this.pushStream.write(arrayBuffer);
    } catch (error) {
      console.error('🎤❌ Failed to send audio to Azure:', error);
      this.handleError(error);
    }
  }

  /**
   * 设置识别器事件监听
   */
  private setupRecognizerEvents(): void {
    if (!this.recognizer) return;

    // 识别中间结果
    this.recognizer.recognizing = (s, e) => {
      if (e.result.text) {
        const result: TranscriptionResult = {
          text: e.result.text,
          isFinal: false,
          confidence: 0.8,
          timestamp: Date.now(),
          language: this.config.language
        };
        this.emit('transcription', result);
      }
    };

    // 识别最终结果
    this.recognizer.recognized = (s, e) => {
      if (e.result.text) {
        const result: TranscriptionResult = {
          text: e.result.text,
          isFinal: true,
          confidence: 0.9,
          timestamp: Date.now(),
          language: this.config.language
        };
        
        console.log(`🎤✅ Azure result: ${result.text}`);
        this.emit('transcription', result);
      }
    };

    // 会话开始
    this.recognizer.sessionStarted = (s, e) => {
      console.log('🎤🎬 Azure session started');
    };

    // 会话停止
    this.recognizer.sessionStopped = (s, e) => {
      console.log('🎤🛑 Azure session stopped');
    };

    // 错误处理
    this.recognizer.canceled = (s, e) => {
      console.log(`🎤❌ Azure recognition canceled: ${e.errorDetails}`);
      
      if (e.reason === sdk.CancellationReason.Error) {
        this.handleError(new Error(e.errorDetails));
      }
    };
  }

  /**
   * 处理错误
   */
  private async handleError(error: any): Promise<void> {
    console.error('🎤❌ Azure transcription error:', error);
    
    const errorMessage = error.message || error.toString();
    
    // 检查是否是认证错误
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('authentication')) {
      console.log('🎤🔑 Authentication error - attempting token refresh...');
      
      if (this.currentAccount) {
        const refreshed = await this.accountManager.refreshAccountToken(this.currentAccount.id);
        
        if (refreshed) {
          console.log('🎤✅ Token refreshed successfully, restarting transcription...');
          this.currentAccount = this.accountManager.getCurrentAccount();
          
          // 重启转录
          await this.stopTranscription();
          setTimeout(async () => {
            await this.startTranscription();
          }, 2000);
        } else {
          console.log('🎤⚠️ Token refresh failed, switching account...');
          await this.switchAccount();
        }
      }
    } else {
      this.emit('error', error);
    }
  }

  /**
   * 切换账号
   */
  private async switchAccount(): Promise<boolean> {
    try {
      const switched = await this.accountManager.switchToNextAccount();
      if (switched) {
        this.currentAccount = this.accountManager.getCurrentAccount();
        console.log(`🎤✅ Switched to account: ${this.currentAccount?.name}`);
        
        // 重启转录
        await this.stopTranscription();
        setTimeout(async () => {
          await this.startTranscription();
        }, 1000);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('🎤❌ Failed to switch account:', error);
      return false;
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    if (this.isTranscribing) {
      return TranscriptionStatus.CONNECTED;
    }
    return TranscriptionStatus.DISCONNECTED;
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }
    
    if (this.pushStream) {
      this.pushStream.close();
      this.pushStream = null;
    }
    
    if (this.audioConfig) {
      this.audioConfig.close();
      this.audioConfig = null;
    }
    
    if (this.speechConfig) {
      this.speechConfig.close();
      this.speechConfig = null;
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    await this.stopTranscription();
    this.accountManager.removeAllListeners();
  }
}
