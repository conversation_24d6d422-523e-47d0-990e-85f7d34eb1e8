import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import OpenAI from 'openai';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * OpenAI特定配置接口
 */
export interface OpenAIConfig extends TranscriptionConfig {
  model?: string;
  prompt?: string;
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
  temperature?: number;
  timestamp_granularities?: ('word' | 'segment')[];
  bufferDuration?: number;
}

/**
 * OpenAI Whisper语音转录服务 - 使用官方SDK
 * 基于OpenAI官方JavaScript SDK
 * 支持批量音频转录（Whisper不支持真正的实时流式）
 */
export class OpenAITranscriptionService extends BaseTranscriptionService {
  private config: OpenAIConfig;
  private isTranscribing = false;
  private openai: OpenAI | null = null;
  private audioBuffer: Buffer[] = [];
  private transcriptionTimer: NodeJS.Timeout | null = null;
  private tempDir: string;

  constructor(config: OpenAIConfig) {
    super(config);
    this.config = {
      realtime: true,
      timeout: 10000,
      retryAttempts: 2,
      model: 'whisper-1',
      response_format: 'verbose_json',
      temperature: 0,
      timestamp_granularities: ['word'],
      bufferDuration: 3000, // 3秒缓冲
      ...config
    };
    
    this.tempDir = path.join(os.tmpdir(), 'openai-transcription');
    this.ensureTempDir();
  }

  /**
   * 确保临时目录存在
   */
  private ensureTempDir(): void {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing OpenAI transcription service...');
      
      if (!this.config.apiKey) {
        throw new Error('OpenAI API key is required');
      }

      // 创建OpenAI客户端
      this.openai = new OpenAI({
        apiKey: this.config.apiKey
      });
      
      console.log('🎤✅ OpenAI transcription service initialized');
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to initialize OpenAI transcription service:', error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    if (this.isTranscribing) {
      console.log('🎤⚠️ OpenAI transcription already running');
      return true;
    }

    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      console.log('🎤 Starting OpenAI transcription...');
      
      this.isTranscribing = true;
      this.audioBuffer = [];
      
      // 启动定期转录处理
      this.startPeriodicTranscription();
      
      console.log('🎤✅ OpenAI transcription started');
      this.emit('status-changed', TranscriptionStatus.CONNECTED);
      
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to start OpenAI transcription:', error);
      this.isTranscribing = false;
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('🎤 Stopping OpenAI transcription...');
    
    this.isTranscribing = false;
    
    if (this.transcriptionTimer) {
      clearInterval(this.transcriptionTimer);
      this.transcriptionTimer = null;
    }
    
    // 处理剩余的音频缓冲
    if (this.audioBuffer.length > 0) {
      await this.processAudioBuffer();
    }
    
    console.log('🎤✅ OpenAI transcription stopped');
    this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.isTranscribing) {
      return;
    }

    // 将音频数据添加到缓冲区
    this.audioBuffer.push(audioData);
  }

  /**
   * 启动定期转录处理
   */
  private startPeriodicTranscription(): void {
    this.transcriptionTimer = setInterval(async () => {
      if (this.audioBuffer.length > 0) {
        await this.processAudioBuffer();
      }
    }, this.config.bufferDuration);
  }

  /**
   * 处理音频缓冲区
   */
  private async processAudioBuffer(): Promise<void> {
    if (this.audioBuffer.length === 0 || !this.openai) {
      return;
    }

    try {
      // 合并音频缓冲区
      const combinedAudio = Buffer.concat(this.audioBuffer);
      this.audioBuffer = [];

      // 转换为WAV格式并保存到临时文件
      const tempFilePath = await this.saveAudioToTempFile(combinedAudio);

      // 发送到OpenAI Whisper API
      const transcriptionResult = await this.callOpenAIWhisperAPI(tempFilePath);
      
      if (transcriptionResult && transcriptionResult.trim()) {
        const result: TranscriptionResult = {
          text: transcriptionResult,
          isFinal: true,
          confidence: 0.9,
          timestamp: Date.now(),
          language: this.config.language
        };

        console.log(`🎤✅ OpenAI result: ${result.text}`);
        this.emit('transcription', result);
      }

      // 清理临时文件
      this.cleanupTempFile(tempFilePath);
    } catch (error) {
      console.error('🎤❌ Failed to process audio buffer:', error);
      this.handleError(error);
    }
  }

  /**
   * 保存音频到临时文件
   */
  private async saveAudioToTempFile(audioData: Buffer): Promise<string> {
    const fileName = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.wav`;
    const filePath = path.join(this.tempDir, fileName);
    
    // 转换为WAV格式
    const wavBuffer = this.convertToWav(audioData);
    
    fs.writeFileSync(filePath, wavBuffer);
    return filePath;
  }

  /**
   * 转换音频为WAV格式
   */
  private convertToWav(pcmData: Buffer): Buffer {
    const sampleRate = 16000;
    const channels = 1;
    const bitsPerSample = 16;
    
    const dataLength = pcmData.length;
    const fileLength = 44 + dataLength;
    
    const header = Buffer.alloc(44);
    
    // WAV文件头
    header.write('RIFF', 0);
    header.writeUInt32LE(fileLength - 8, 4);
    header.write('WAVE', 8);
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16);
    header.writeUInt16LE(1, 20);
    header.writeUInt16LE(channels, 22);
    header.writeUInt32LE(sampleRate, 24);
    header.writeUInt32LE(sampleRate * channels * bitsPerSample / 8, 28);
    header.writeUInt16LE(channels * bitsPerSample / 8, 32);
    header.writeUInt16LE(bitsPerSample, 34);
    header.write('data', 36);
    header.writeUInt32LE(dataLength, 40);
    
    return Buffer.concat([header, pcmData]);
  }

  /**
   * 调用OpenAI Whisper API
   */
  private async callOpenAIWhisperAPI(audioFilePath: string): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const transcription = await this.openai.audio.transcriptions.create({
        file: fs.createReadStream(audioFilePath),
        model: this.config.model || 'whisper-1',
        language: this.config.language,
        prompt: this.config.prompt,
        response_format: this.config.response_format,
        temperature: this.config.temperature,
        timestamp_granularities: this.config.timestamp_granularities
      });

      if (this.config.response_format === 'verbose_json') {
        return (transcription as any).text || '';
      } else {
        return transcription as string;
      }
    } catch (error) {
      console.error('🎤❌ OpenAI API error:', error);
      throw error;
    }
  }

  /**
   * 清理临时文件
   */
  private cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error('🎤⚠️ Failed to cleanup temp file:', error);
    }
  }

  /**
   * 处理错误
   */
  private async handleError(error: any): Promise<void> {
    console.error('🎤❌ OpenAI transcription error:', error);
    
    const errorMessage = error.message || error.toString();
    
    // 检查是否是认证错误
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('authentication')) {
      console.log('🎤🔑 Authentication error detected');
      this.emit('error', new Error('OpenAI authentication failed. Please check your API key.'));
    } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      console.log('🎤⏰ Rate limit error detected');
      this.emit('error', new Error('OpenAI rate limit exceeded. Please try again later.'));
    } else if (errorMessage.includes('quota')) {
      console.log('🎤💰 Quota error detected');
      this.emit('error', new Error('OpenAI quota exceeded. Please check your billing.'));
    } else {
      this.emit('error', error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    if (this.isTranscribing) {
      return TranscriptionStatus.CONNECTED;
    }
    return TranscriptionStatus.DISCONNECTED;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopTranscription();
    
    // 清理临时目录
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        for (const file of files) {
          fs.unlinkSync(path.join(this.tempDir, file));
        }
        fs.rmdirSync(this.tempDir);
      }
    } catch (error) {
      console.error('🎤⚠️ Failed to cleanup temp directory:', error);
    }
    
    this.openai = null;
  }
}
