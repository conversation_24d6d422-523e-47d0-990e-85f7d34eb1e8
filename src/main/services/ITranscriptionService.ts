import { EventEmitter } from 'events';

export enum TranscriptionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  TRANSCRIBING = 'transcribing',
  ERROR = 'error'
}

export interface TranscriptionResult {
  text: string;
  isFinal: boolean;
  confidence?: number;
  timestamp?: number;
  speaker?: string;
  language?: string;
}

export interface TranscriptionConfig {
  apiKey?: string;
  language?: string;
  model?: string;
  realtime?: boolean;
  smartFormat?: boolean;
  punctuate?: boolean;
  diarize?: boolean;
  numerals?: boolean;
  interim_results?: boolean;
  endpointing?: number;
}

export interface ITranscriptionService {
  initialize(): Promise<boolean>;
  startTranscription(): Promise<boolean>;
  stopTranscription(): Promise<void>;
  sendAudio(audioData: Buffer): Promise<void>;
  getStatus(): TranscriptionStatus;
  cleanup(): void;
  
  // 事件监听
  onTranscription(callback: (result: TranscriptionResult) => void): void;
  onError(callback: (error: Error) => void): void;
  onStatusChange(callback: (status: TranscriptionStatus) => void): void;
}

/**
 * 转录服务基类
 */
export abstract class BaseTranscriptionService extends EventEmitter implements ITranscriptionService {
  protected config: TranscriptionConfig;
  protected status: TranscriptionStatus = TranscriptionStatus.DISCONNECTED;

  constructor(config: TranscriptionConfig) {
    super();
    this.config = config;
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    return this.status;
  }

  /**
   * 更新服务状态
   */
  protected updateStatus(status: TranscriptionStatus): void {
    if (this.status !== status) {
      this.status = status;
      this.emit('status-change', status);
    }
  }

  /**
   * 发送转录结果
   */
  protected emitTranscription(result: TranscriptionResult): void {
    this.emit('transcription', result);
  }

  /**
   * 发送错误
   */
  protected emitError(error: string | Error): void {
    const err = typeof error === 'string' ? new Error(error) : error;
    this.emit('error', err);
  }

  /**
   * 设置转录结果回调
   */
  onTranscription(callback: (result: TranscriptionResult) => void): void {
    this.on('transcription', callback);
  }

  /**
   * 设置错误回调
   */
  onError(callback: (error: Error) => void): void {
    this.on('error', callback);
  }

  /**
   * 设置连接状态回调
   */
  onStatusChange(callback: (status: TranscriptionStatus) => void): void {
    this.on('status-change', callback);
  }

  /**
   * 获取配置
   */
  getConfig(): TranscriptionConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<TranscriptionConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.removeAllListeners();
    this.status = TranscriptionStatus.DISCONNECTED;
  }

  // 抽象方法，子类必须实现
  abstract initialize(): Promise<boolean>;
  abstract startTranscription(): Promise<boolean>;
  abstract stopTranscription(): Promise<void>;
  abstract sendAudio(audioData: Buffer): Promise<void>;
}
