import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import * as aai from 'assemblyai';

/**
 * AssemblyAI特定配置接口
 */
export interface AssemblyAIConfig extends TranscriptionConfig {
  model?: string;
  formatTurns?: boolean;
  endOfTurnConfidenceThreshold?: number;
  minEndOfTurnSilenceWhenConfident?: number;
  maxTurnSilence?: number;
  encoding?: string;
  sampleRate?: number;
}

/**
 * AssemblyAI语音转录服务 - 使用官方SDK
 * 基于AssemblyAI官方JavaScript SDK
 * 支持Universal-Streaming实时转录
 */
export class AssemblyAITranscriptionService extends BaseTranscriptionService {
  private config: AssemblyAIConfig;
  private isTranscribing = false;
  private client: aai.StreamingClient | null = null;
  private audioStream: any = null;

  constructor(config: AssemblyAIConfig) {
    super(config);
    this.config = {
      realtime: true,
      timeout: 10000,
      retryAttempts: 2,
      formatTurns: true,
      endOfTurnConfidenceThreshold: 0.7,
      minEndOfTurnSilenceWhenConfident: 160,
      maxTurnSilence: 2400,
      encoding: 'pcm_s16le',
      sampleRate: 16000,
      ...config
    };
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing AssemblyAI transcription service...');
      
      if (!this.config.apiKey) {
        throw new Error('AssemblyAI API key is required');
      }

      // 创建AssemblyAI流式客户端
      this.client = new aai.StreamingClient({
        apiKey: this.config.apiKey,
        apiHost: 'streaming.assemblyai.com'
      });
      
      console.log('🎤✅ AssemblyAI transcription service initialized');
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to initialize AssemblyAI transcription service:', error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    if (this.isTranscribing) {
      console.log('🎤⚠️ AssemblyAI transcription already running');
      return true;
    }

    if (!this.client) {
      throw new Error('AssemblyAI client not initialized');
    }

    try {
      console.log('🎤 Starting AssemblyAI transcription...');
      
      // 设置事件监听器
      this.setupClientEvents();
      
      // 连接到AssemblyAI
      this.client.connect({
        sampleRate: this.config.sampleRate || 16000,
        formatTurns: this.config.formatTurns,
        endOfTurnConfidenceThreshold: this.config.endOfTurnConfidenceThreshold,
        minEndOfTurnSilenceWhenConfident: this.config.minEndOfTurnSilenceWhenConfident,
        maxTurnSilence: this.config.maxTurnSilence
      });
      
      this.isTranscribing = true;
      console.log('🎤✅ AssemblyAI transcription started');
      this.emit('status-changed', TranscriptionStatus.CONNECTED);
      
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to start AssemblyAI transcription:', error);
      this.isTranscribing = false;
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('🎤 Stopping AssemblyAI transcription...');
    
    this.isTranscribing = false;
    
    if (this.client) {
      try {
        this.client.disconnect(true);
      } catch (error) {
        console.error('🎤❌ Error stopping AssemblyAI connection:', error);
      }
    }
    
    console.log('🎤✅ AssemblyAI transcription stopped');
    this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.isTranscribing || !this.client) {
      return;
    }

    try {
      // AssemblyAI期望Buffer格式的音频数据
      this.client.sendAudio(audioData);
    } catch (error) {
      console.error('🎤❌ Failed to send audio to AssemblyAI:', error);
      this.handleError(error);
    }
  }

  /**
   * 设置客户端事件监听
   */
  private setupClientEvents(): void {
    if (!this.client) return;

    // 会话开始
    this.client.on(aai.StreamingEvents.Begin, (event: any) => {
      console.log(`🎤🎬 AssemblyAI session started: ${event.id}`);
    });

    // 转录结果
    this.client.on(aai.StreamingEvents.Turn, (event: any) => {
      try {
        const transcript = event.transcript?.trim();
        if (transcript) {
          const result: TranscriptionResult = {
            text: transcript,
            isFinal: event.end_of_turn || event.turn_is_formatted || false,
            confidence: event.end_of_turn_confidence || 0.8,
            timestamp: Date.now(),
            language: this.config.language
          };

          console.log(`🎤${result.isFinal ? '✅' : '📝'} AssemblyAI result: ${result.text}`);
          this.emit('transcription', result);
        }
      } catch (error) {
        console.error('🎤❌ Error processing AssemblyAI transcript:', error);
      }
    });

    // 会话终止
    this.client.on(aai.StreamingEvents.Termination, (event: any) => {
      console.log(`🎤🛑 AssemblyAI session terminated: ${event.audio_duration_seconds}s processed`);
      this.isTranscribing = false;
      this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
    });

    // 错误处理
    this.client.on(aai.StreamingEvents.Error, (error: any) => {
      console.error('🎤❌ AssemblyAI error:', error);
      this.handleError(error);
    });
  }

  /**
   * 处理错误
   */
  private async handleError(error: any): Promise<void> {
    console.error('🎤❌ AssemblyAI transcription error:', error);
    
    const errorMessage = error.message || error.toString();
    
    // 检查是否是认证错误
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized') || errorMessage.includes('authentication')) {
      console.log('🎤🔑 Authentication error detected');
      this.emit('error', new Error('AssemblyAI authentication failed. Please check your API key.'));
    } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      console.log('🎤⏰ Rate limit error detected');
      this.emit('error', new Error('AssemblyAI rate limit exceeded. Please try again later.'));
    } else if (errorMessage.includes('3005')) {
      console.log('🎤⚠️ Session error detected');
      this.emit('error', new Error('AssemblyAI session error. Please check audio format and parameters.'));
    } else {
      this.emit('error', error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    if (this.isTranscribing && this.client) {
      return TranscriptionStatus.CONNECTED;
    }
    return TranscriptionStatus.DISCONNECTED;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopTranscription();
    this.client = null;
  }
}
