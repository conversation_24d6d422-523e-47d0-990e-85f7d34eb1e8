import { ITranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { EventEmitter } from 'events';
import WebSocket from 'ws';

export interface GladiaConfig extends TranscriptionConfig {
  apiKey: string;
  language?: string;
  model?: string;
  realtime?: boolean;
  punctuation?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * Gladia 转录服务
 * 提供实时语音转录功能
 */
export class GladiaTranscriptionService extends EventEmitter implements ITranscriptionService {
  private config: GladiaConfig;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isTranscribing = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private sessionId: string | null = null;

  constructor(config: GladiaConfig) {
    super();
    this.config = {
      realtime: true,
      punctuation: true,
      timeout: 10000,
      retryAttempts: 2,
      ...config
    };
    console.log('🎭 GladiaTranscriptionService created');
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎭 Initializing Gladia transcription service...');

      if (!this.config.apiKey) {
        throw new Error('Gladia API key is required');
      }

      console.log('🎭✅ Gladia transcription service initialized successfully');
      return true;
    } catch (error) {
      console.error('🎭❌ Failed to initialize Gladia transcription service:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    try {
      if (this.isTranscribing) {
        console.log('🎭 Transcription already active');
        return true;
      }

      console.log('🎭 Starting Gladia transcription...');
      this.updateStatus(TranscriptionStatus.CONNECTING);

      // 创建WebSocket连接
      const success = await this.createWebSocketConnection();
      
      if (success) {
        this.isTranscribing = true;
        this.updateStatus(TranscriptionStatus.LISTENING);
        console.log('🎭✅ Gladia transcription started');
        return true;
      } else {
        throw new Error('Failed to establish WebSocket connection');
      }
    } catch (error) {
      console.error('🎭❌ Failed to start Gladia transcription:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    try {
      console.log('🎭 Stopping Gladia transcription...');
      
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      this.isTranscribing = false;
      this.isConnected = false;
      this.sessionId = null;
      this.clearReconnectTimeout();
      
      this.updateStatus(TranscriptionStatus.DISCONNECTED);
      console.log('🎭✅ Gladia transcription stopped');
    } catch (error) {
      console.error('🎭❌ Failed to stop Gladia transcription:', error);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.ws || !this.isConnected) {
      return;
    }

    try {
      // 根据Gladia文档，直接发送二进制音频数据
      this.ws.send(audioData);
    } catch (error) {
      console.error('🎭❌ Failed to send audio to Gladia:', error);
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('https://api.gladia.io/v2/pre-recorded/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          audio_url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
          language: this.config.language || 'zh'
        })
      });

      return response.ok;
    } catch (error) {
      console.error('🎭❌ Gladia connection test failed:', error);
      return false;
    }
  }

  /**
   * 创建WebSocket连接
   */
  private async createWebSocketConnection(): Promise<boolean> {
    try {
      // 第一步：初始化Gladia会话
      console.log('🎭 Initializing Gladia session...');
      const initResponse = await fetch('https://api.gladia.io/v2/live', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Gladia-Key': this.config.apiKey,
        },
        body: JSON.stringify({
          encoding: 'wav/pcm',
          sample_rate: 16000,
          bit_depth: 16,
          channels: 1
        }),
      });

      if (!initResponse.ok) {
        const errorText = await initResponse.text();
        console.error('🎭❌ Gladia API error:', initResponse.status, errorText);
        return false;
      }

      const { id, url } = await initResponse.json();
      this.sessionId = id;

      console.log('🎭 Gladia session created:', id);
      console.log('🎭 Connecting to WebSocket:', url);

      // 第二步：连接到WebSocket
      return new Promise((resolve) => {
        this.ws = new WebSocket(url);

        let hasResolved = false;

        this.ws.on('open', () => {
          console.log('🎭✅ Gladia WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          if (!hasResolved) {
            hasResolved = true;
            resolve(true);
          }
        });

        this.ws.on('message', (data) => {
          this.handleWebSocketMessage(data);
        });

        this.ws.on('error', (error) => {
          console.error('🎭❌ Gladia WebSocket error:', error);
          this.handleError(error);
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        this.ws.on('close', (code, reason) => {
          console.log(`🎭 Gladia WebSocket closed: ${code} ${reason}`);
          this.isConnected = false;
          
          if (this.isTranscribing) {
            this.attemptReconnect();
          }
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        // 超时处理
        setTimeout(() => {
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        }, this.config.timeout || 10000);
      });
    } catch (error) {
      console.error('🎭❌ Failed to create Gladia WebSocket:', error);
      return false;
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'transcript':
          this.handleTranscriptMessage(message);
          break;
        case 'audio_chunk':
          // 音频数据确认消息，通常可以忽略
          console.log('🎭📤 Audio chunk received by Gladia');
          break;
        case 'error':
          this.handleError(new Error(message.message || 'Gladia API error'));
          break;
        case 'status':
          console.log('🎭 Gladia status:', message.status);
          break;
        default:
          console.log('🎭 Unknown Gladia message type:', message.type);
      }
    } catch (error) {
      console.error('🎭❌ Failed to parse Gladia message:', error);
    }
  }

  /**
   * 处理转录消息
   */
  private handleTranscriptMessage(message: any): void {
    try {
      // Gladia V2 API的消息格式：message.data.utterance.text
      const transcript = message.data?.utterance?.text;
      if (!transcript || !transcript.trim()) {
        return;
      }

      const result: TranscriptionResult = {
        text: transcript,
        isFinal: message.data?.is_final || false,
        confidence: message.confidence || 0,
        timestamp: Date.now(),
        language: message.language || this.config.language || 'zh',
        alternatives: message.alternatives || []
      };

      if (result.text && result.text.trim()) {
        console.log('🎭 Gladia transcription:', result.text);
        this.emit('transcription', result);
      }
    } catch (error) {
      console.error('🎭❌ Failed to handle Gladia transcript:', error);
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🎭❌ Max reconnect attempts reached');
      this.updateStatus(TranscriptionStatus.ERROR);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);
    
    console.log(`🎭 Attempting to reconnect to Gladia (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
    
    this.reconnectTimeout = setTimeout(async () => {
      this.updateStatus(TranscriptionStatus.RECONNECTING);
      const success = await this.createWebSocketConnection();
      
      if (!success) {
        this.attemptReconnect();
      } else {
        this.updateStatus(TranscriptionStatus.LISTENING);
      }
    }, delay);
  }

  /**
   * 清除重连超时
   */
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 更新状态
   */
  private updateStatus(status: TranscriptionStatus): void {
    this.emit('statusChange', status);
  }

  /**
   * 处理错误
   */
  private handleError(error: any): void {
    const errorMessage = typeof error === 'string' ? error : error.message || 'Unknown error';
    console.error('🎭❌ Gladia transcription error:', errorMessage);
    this.emit('error', new Error(errorMessage));
    this.updateStatus(TranscriptionStatus.ERROR);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopTranscription();
    this.removeAllListeners();
  }
}
