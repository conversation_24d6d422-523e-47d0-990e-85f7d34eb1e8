import { EventEmitter } from 'events';
import Groq from 'groq-sdk';

export interface GroqConfig {
  apiKey: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[] | null;
}

export interface AIResponse {
  text: string;
  processingTime: number;
  timestamp: number;
}

export interface InterviewContext {
  position?: string;
  company?: string;
  experience?: string;
  skills?: string[];
  interviewType?: string;
  difficulty?: string;
}

export class GroqAIService extends EventEmitter {
  private client: Groq | null = null;
  private config: GroqConfig;
  private isConnected = false;
  private requestCount = 0;
  private lastRequestTime = 0;

  constructor(config: GroqConfig) {
    super();
    this.config = config;
    console.log('GroqAIService created with config:', {
      model: config.model,
      maxTokens: config.maxTokens,
      temperature: config.temperature
    });
  }

  /**
   * 初始化Groq客户端
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing Groq client...');
      
      if (!this.config.apiKey) {
        throw new Error('Groq API key is required');
      }

      this.client = new Groq({
        apiKey: this.config.apiKey
      });

      // 测试连接
      await this.testConnection();
      
      this.isConnected = true;
      console.log('Groq client initialized successfully');
      this.emit('connected');
      return true;
    } catch (error) {
      console.error('Failed to initialize Groq client:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    if (!this.client) {
      throw new Error('Groq client not initialized');
    }

    try {
      await this.client.chat.completions.create({
        model: this.config.model || 'llama-3.1-8b-instant',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10
      });
    } catch (error) {
      throw new Error(`Groq connection test failed: ${error}`);
    }
  }

  /**
   * 生成AI回答
   */
  async generateAnswer(question: string, context?: InterviewContext): Promise<string> {
    if (!this.client || !this.isConnected) {
      throw new Error('Groq client not initialized or connected');
    }

    try {
      console.log('Generating answer with Groq:', { question: question.substring(0, 100) + '...' });
      
      const startTime = Date.now();
      this.requestCount++;

      const systemPrompt = this.buildSystemPrompt(context);
      const messages = this.buildMessages(systemPrompt, question, context);

      const response = await this.client.chat.completions.create({
        model: this.config.model || 'llama-3.1-8b-instant',
        messages,
        max_tokens: this.config.maxTokens || 500,
        temperature: this.config.temperature || 0.7,
        top_p: this.config.topP || 1,
        frequency_penalty: this.config.frequencyPenalty || 0,
        presence_penalty: this.config.presencePenalty || 0,
        stop: this.config.stop || null,
        stream: false
      });

      const processingTime = Date.now() - startTime;
      this.lastRequestTime = processingTime;

      const answer = response.choices[0]?.message?.content || '';

      console.log('Groq response generated:', {
        responseTime: `${processingTime}ms`,
        answerLength: answer.length,
        requestCount: this.requestCount
      });

      this.emit('response', {
        text: answer,
        processingTime,
        timestamp: Date.now()
      });

      return answer;
    } catch (error) {
      console.error('Error generating answer with Groq:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 流式生成AI回答
   */
  async generateAnswerStream(
    question: string, 
    context: InterviewContext | undefined, 
    onChunk: (chunk: string) => void
  ): Promise<string> {
    if (!this.client || !this.isConnected) {
      throw new Error('Groq client not initialized or connected');
    }

    try {
      console.log('Generating streaming answer with Groq:', { question: question.substring(0, 100) + '...' });
      
      const startTime = Date.now();
      this.requestCount++;

      const systemPrompt = this.buildSystemPrompt(context);
      const messages = this.buildMessages(systemPrompt, question, context);

      const stream = await this.client.chat.completions.create({
        model: this.config.model || 'llama-3.1-8b-instant',
        messages,
        max_tokens: this.config.maxTokens || 500,
        temperature: this.config.temperature || 0.7,
        top_p: this.config.topP || 1,
        frequency_penalty: this.config.frequencyPenalty || 0,
        presence_penalty: this.config.presencePenalty || 0,
        stop: this.config.stop || null,
        stream: true
      });

      let fullAnswer = '';

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullAnswer += content;
          onChunk(content);
        }
      }

      const processingTime = Date.now() - startTime;
      this.lastRequestTime = processingTime;

      console.log('Groq streaming response completed:', {
        responseTime: `${processingTime}ms`,
        answerLength: fullAnswer.length,
        requestCount: this.requestCount
      });

      this.emit('response', {
        text: fullAnswer,
        processingTime,
        timestamp: Date.now()
      });

      return fullAnswer;
    } catch (error) {
      console.error('Error generating streaming answer with Groq:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(context?: InterviewContext): string {
    let prompt = `你是一个专业的面试助手，专门帮助候选人在面试中提供智能回答建议。

核心原则：
1. 提供简洁、专业、真实的回答建议
2. 回答要符合中国职场文化和习惯
3. 避免过度夸张或虚假的表述
4. 重点突出候选人的能力和经验
5. 保持积极正面的态度

回答格式：
- 直接给出可以说的回答内容
- 语言自然流畅，适合口语表达
- 控制在30-60秒的说话时长
- 重点突出，逻辑清晰`;

    if (context) {
      if (context.position) {
        prompt += `\n\n应聘职位：${context.position}`;
      }
      if (context.company) {
        prompt += `\n公司：${context.company}`;
      }
      if (context.experience) {
        prompt += `\n工作经验：${context.experience}`;
      }
      if (context.skills && context.skills.length > 0) {
        prompt += `\n技能专长：${context.skills.join(', ')}`;
      }
      if (context.interviewType) {
        prompt += `\n面试类型：${context.interviewType}`;
      }
    }

    return prompt;
  }

  /**
   * 构建消息数组
   */
  private buildMessages(systemPrompt: string, question: string, context?: InterviewContext): any[] {
    return [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: `面试官问题：${question}\n\n请提供一个专业的回答建议。` }
    ];
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      isConnected: this.isConnected
    };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.isConnected = false;
    this.removeAllListeners();
  }
}
