import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { AzureAccountManager, AzureAccount } from './AzureAccountManager';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Azure特定配置接口
 */
export interface AzureConfig extends TranscriptionConfig {
  region?: string;
  recognitionMode?: 'conversation' | 'dictation';
  profanityOption?: 'masked' | 'removed' | 'raw';
  outputFormat?: 'simple' | 'detailed';
  punctuate?: boolean;
  sampleRate?: number;
  channels?: number;
}

/**
 * Azure语音转录服务 - 使用正确的REST API方法
 * 基于Microsoft Azure Speech Services REST API
 * 支持多账号自动切换和使用时长管理
 */
export class AzureTranscriptionServiceNew extends BaseTranscriptionService {
  private config: AzureConfig;
  private isTranscribing = false;
  private accountManager: AzureAccountManager;
  private currentAccount: AzureAccount | null = null;
  private audioBuffer: Buffer[] = [];
  private transcriptionTimer: NodeJS.Timeout | null = null;
  private bufferDuration = 3000; // 3秒缓冲

  constructor(config: AzureConfig) {
    super(config);
    this.config = {
      realtime: true,
      punctuate: true,
      timeout: 10000,
      retryAttempts: 2,
      sampleRate: 16000,
      channels: 1,
      recognitionMode: 'conversation',
      profanityOption: 'masked',
      outputFormat: 'simple',
      ...config
    };

    this.accountManager = new AzureAccountManager();
    this.setupAccountManager();
  }

  /**
   * 设置账号管理器事件监听
   */
  private setupAccountManager(): void {
    this.accountManager.on('account-switched', (account: AzureAccount) => {
      console.log(`🔑 Account switched to: ${account.name}`);
      this.currentAccount = account;
    });

    this.accountManager.on('account-expired', (accountId: string) => {
      console.log(`🔑⏰ Account expired: ${accountId}`);
      this.switchAccount();
    });

    this.accountManager.on('no-accounts-available', () => {
      console.error('🔑❌ No Azure accounts available');
      this.emit('error', new Error('No Azure accounts available'));
    });
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      console.log('🎤 Initializing Azure transcription service...');

      await this.accountManager.initialize();
      this.currentAccount = this.accountManager.getCurrentAccount();

      // 如果没有活跃账号，尝试刷新第一个账号的token
      if (!this.currentAccount) {
        console.log('🎤🔑 No active accounts, attempting to refresh tokens...');
        const accounts = this.accountManager.getAllAccounts();

        if (accounts.length > 0) {
          const firstAccount = accounts[0];
          console.log(`🎤🔄 Attempting to refresh token for: ${firstAccount.name}`);

          const refreshed = await this.accountManager.refreshAccountToken(firstAccount.id);
          if (refreshed) {
            this.currentAccount = this.accountManager.getCurrentAccount();
            console.log(`🎤✅ Token refreshed successfully for: ${firstAccount.name}`);
          } else {
            console.log('🎤⚠️ Token refresh failed, will retry during transcription');
            // 不抛出错误，允许服务初始化，在转录时再尝试刷新
            this.currentAccount = firstAccount; // 临时使用过期账号
          }
        } else {
          throw new Error('No Azure accounts configured');
        }
      }

      console.log(`🎤✅ Azure transcription service initialized with account: ${this.currentAccount?.name || 'Unknown'}`);
    } catch (error) {
      console.error('🎤❌ Failed to initialize Azure transcription service:', error);
      throw error;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<void> {
    if (this.isTranscribing) {
      console.log('🎤⚠️ Azure transcription already running');
      return;
    }

    if (!this.currentAccount) {
      throw new Error('No current Azure account available');
    }

    try {
      console.log('🎤 Starting Azure transcription...');
      this.isTranscribing = true;
      this.audioBuffer = [];
      
      // 启动定期转录处理
      this.startPeriodicTranscription();
      
      console.log('🎤✅ Azure transcription started');
      this.emit('status-changed', TranscriptionStatus.CONNECTED);
    } catch (error) {
      console.error('🎤❌ Failed to start Azure transcription:', error);
      this.isTranscribing = false;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('🎤 Stopping Azure transcription...');
    
    this.isTranscribing = false;
    
    if (this.transcriptionTimer) {
      clearInterval(this.transcriptionTimer);
      this.transcriptionTimer = null;
    }
    
    // 处理剩余的音频缓冲
    if (this.audioBuffer.length > 0) {
      await this.processAudioBuffer();
    }
    
    console.log('🎤✅ Azure transcription stopped');
    this.emit('status-changed', TranscriptionStatus.DISCONNECTED);
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.isTranscribing) {
      return;
    }

    // 将音频数据添加到缓冲区
    this.audioBuffer.push(audioData);
  }

  /**
   * 启动定期转录处理
   */
  private startPeriodicTranscription(): void {
    this.transcriptionTimer = setInterval(async () => {
      if (this.audioBuffer.length > 0) {
        await this.processAudioBuffer();
      }
    }, this.bufferDuration);
  }

  /**
   * 处理音频缓冲区
   */
  private async processAudioBuffer(): Promise<void> {
    if (this.audioBuffer.length === 0 || !this.currentAccount) {
      return;
    }

    try {
      // 合并音频缓冲区
      const combinedAudio = Buffer.concat(this.audioBuffer);
      this.audioBuffer = [];

      // 转换为WAV格式
      const wavBuffer = this.convertToWav(combinedAudio);

      // 发送到Azure Speech API
      const transcriptionResult = await this.callAzureSpeechAPI(wavBuffer);
      
      if (transcriptionResult && transcriptionResult.trim()) {
        const result: TranscriptionResult = {
          text: transcriptionResult,
          isFinal: true,
          confidence: 0.9,
          timestamp: Date.now(),
          language: this.config.language
        };

        console.log(`🎤✅ Azure result: ${result.text}`);
        this.emit('transcription', result);
      }
    } catch (error) {
      console.error('🎤❌ Failed to process audio buffer:', error);
      
      // 如果是认证错误，尝试刷新token
      if (error.message.includes('401') || error.message.includes('403')) {
        await this.handleAuthError();
      }
    }
  }

  /**
   * 调用Azure Speech REST API
   */
  private async callAzureSpeechAPI(audioData: Buffer): Promise<string> {
    if (!this.currentAccount) {
      throw new Error('No current Azure account');
    }

    const region = this.currentAccount.azureCredentials.region;
    const token = this.currentAccount.azureCredentials.token;
    
    const url = `https://${region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1`;
    const params = new URLSearchParams({
      language: this.config.language || 'zh-CN',
      format: this.config.outputFormat || 'simple',
      profanity: this.config.profanityOption || 'masked'
    });

    return new Promise((resolve, reject) => {
      const options = {
        hostname: `${region}.stt.speech.microsoft.com`,
        path: `/speech/recognition/conversation/cognitiveservices/v1?${params.toString()}`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'audio/wav',
          'Content-Length': audioData.length,
          'Accept': 'application/json'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            if (res.statusCode === 200) {
              const result = JSON.parse(data);
              resolve(result.DisplayText || result.NBest?.[0]?.Display || '');
            } else {
              reject(new Error(`Azure API error: ${res.statusCode} - ${data}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse Azure response: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(audioData);
      req.end();
    });
  }

  /**
   * 转换音频为WAV格式
   */
  private convertToWav(pcmData: Buffer): Buffer {
    const sampleRate = this.config.sampleRate || 16000;
    const channels = this.config.channels || 1;
    const bitsPerSample = 16;
    
    const dataLength = pcmData.length;
    const fileLength = 44 + dataLength;
    
    const header = Buffer.alloc(44);
    
    // WAV文件头
    header.write('RIFF', 0);
    header.writeUInt32LE(fileLength - 8, 4);
    header.write('WAVE', 8);
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16);
    header.writeUInt16LE(1, 20);
    header.writeUInt16LE(channels, 22);
    header.writeUInt32LE(sampleRate, 24);
    header.writeUInt32LE(sampleRate * channels * bitsPerSample / 8, 28);
    header.writeUInt16LE(channels * bitsPerSample / 8, 32);
    header.writeUInt16LE(bitsPerSample, 34);
    header.write('data', 36);
    header.writeUInt32LE(dataLength, 40);
    
    return Buffer.concat([header, pcmData]);
  }

  /**
   * 处理认证错误
   */
  private async handleAuthError(): Promise<void> {
    if (!this.currentAccount) {
      return;
    }

    try {
      console.log('🎤🔑 Authentication error - attempting token refresh...');
      const refreshed = await this.accountManager.refreshAccountToken(this.currentAccount.id);
      
      if (refreshed) {
        console.log('🎤✅ Token refreshed successfully');
        this.currentAccount = this.accountManager.getCurrentAccount();
      } else {
        console.log('🎤⚠️ Token refresh failed, switching account...');
        await this.switchAccount();
      }
    } catch (error) {
      console.error('🎤❌ Failed to handle auth error:', error);
    }
  }

  /**
   * 切换账号
   */
  private async switchAccount(): Promise<boolean> {
    try {
      const switched = await this.accountManager.switchToNextAccount();
      if (switched) {
        this.currentAccount = this.accountManager.getCurrentAccount();
        console.log(`🎤✅ Switched to account: ${this.currentAccount?.name}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('🎤❌ Failed to switch account:', error);
      return false;
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): TranscriptionStatus {
    if (this.isTranscribing) {
      return TranscriptionStatus.CONNECTED;
    }
    return TranscriptionStatus.DISCONNECTED;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopTranscription();
    this.accountManager.removeAllListeners();
  }
}
