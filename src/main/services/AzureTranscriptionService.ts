import { EventEmitter } from 'events';
import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { AzureAccountManager, AzureAccount } from './AzureAccountManager';
import * as sdk from 'microsoft-cognitiveservices-speech-sdk';

/**
 * Azure特定配置接口
 */
export interface AzureConfig extends TranscriptionConfig {
  region?: string;
  recognitionMode?: 'conversation' | 'dictation' | 'interactive';
  profanityOption?: 'masked' | 'removed' | 'raw';
  outputFormat?: 'simple' | 'detailed';
  sampleRate?: number;
  channels?: number;
}

/**
 * Azure语音转录服务
 * 提供基于Microsoft Azure Speech Services的实时语音转录功能
 * 支持多账号自动切换和使用时长管理
 */
export class AzureTranscriptionService extends BaseTranscriptionService {
  private config: AzureConfig;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isTranscribing = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private connectionId: string | null = null;
  private accountManager: AzureAccountManager;
  private currentAccount: AzureAccount | null = null;

  constructor(config: AzureConfig) {
    super(config);
    this.config = {
      realtime: true,
      punctuate: true,
      timeout: 10000,
      retryAttempts: 2,
      sampleRate: 16000,
      channels: 1,
      recognitionMode: 'conversation',
      profanityOption: 'masked',
      outputFormat: 'simple',
      ...config
    };
    
    this.accountManager = new AzureAccountManager();
    this.setupAccountManagerEvents();
    
    console.log('🎤 AzureTranscriptionService created');
    console.log('🎤 Config language:', this.config.language);
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing Azure transcription service...');
      
      // 初始化账号管理器
      const accountManagerReady = await this.accountManager.initialize();
      if (!accountManagerReady) {
        throw new Error('Failed to initialize Azure account manager');
      }
      
      // 获取当前账号
      this.currentAccount = this.accountManager.getCurrentAccount();
      if (!this.currentAccount) {
        throw new Error('No available Azure accounts');
      }
      
      console.log(`🎤✅ Azure transcription service initialized with account: ${this.currentAccount.name}`);
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to initialize Azure transcription service:', error);
      this.emitError(error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    try {
      if (this.isTranscribing) {
        console.log('🎤 Transcription already active');
        return true;
      }

      console.log('🎤 Starting Azure transcription...');
      this.updateStatus(TranscriptionStatus.CONNECTING);

      // 确保有可用账号
      this.currentAccount = this.accountManager.getCurrentAccount();
      if (!this.currentAccount) {
        throw new Error('No available Azure accounts');
      }

      // 创建WebSocket连接
      const success = await this.createWebSocketConnection();
      
      if (success) {
        this.isTranscribing = true;
        this.updateStatus(TranscriptionStatus.TRANSCRIBING);
        
        // 开始使用会话计时
        this.accountManager.startSession();
        
        console.log('🎤✅ Azure transcription started');
        return true;
      } else {
        throw new Error('Failed to establish WebSocket connection');
      }
    } catch (error) {
      console.error('🎤❌ Failed to start Azure transcription:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    try {
      console.log('🎤 Stopping Azure transcription...');
      
      this.isTranscribing = false;
      
      // 结束使用会话计时
      this.accountManager.endSession();
      
      if (this.ws) {
        // 发送停止消息
        const stopMessage = {
          speech: {
            context: {
              system: {
                version: "1.0.0"
              }
            }
          }
        };
        
        this.ws.send(JSON.stringify(stopMessage));
        
        // 关闭连接
        this.ws.close();
        this.ws = null;
      }
      
      this.isConnected = false;
      this.connectionId = null;
      this.updateStatus(TranscriptionStatus.DISCONNECTED);
      
      console.log('🎤✅ Azure transcription stopped');
    } catch (error) {
      console.error('🎤❌ Error stopping Azure transcription:', error);
      this.emitError(error);
    }
  }

  /**
   * 发送音频数据 - Azure Speech API格式
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.ws || !this.isConnected || !this.isTranscribing) {
      return;
    }

    try {
      // Azure Speech WebSocket音频消息格式
      const timestamp = new Date().toISOString();
      const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 构建音频消息头 - 精确的Azure格式
      const headers = [
        'Content-Type:audio/x-wav',
        `X-RequestId:${requestId}`,
        `X-Timestamp:${timestamp}`,
        'Path:audio'
      ].join('\r\n');

      const headerText = headers + '\r\n\r\n';
      const headerBuffer = Buffer.from(headerText, 'utf8');

      // Azure要求：2字节头部长度 + 头部 + 音频数据
      const headerLength = Buffer.alloc(2);
      headerLength.writeUInt16BE(headerBuffer.length, 0);

      const message = Buffer.concat([headerLength, headerBuffer, audioData]);

      this.ws.send(message);
    } catch (error) {
      console.error('🎤❌ Failed to send audio to Azure:', error);
      this.handleError(error);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    console.log('🎤 Cleaning up Azure transcription service...');
    
    this.isTranscribing = false;
    this.isConnected = false;
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    // 清理账号管理器
    this.accountManager.cleanup();
    
    // 清理基类资源
    super.cleanup();
    
    console.log('🎤✅ Azure transcription service cleaned up');
  }

  /**
   * 手动切换账号
   */
  async switchAccount(): Promise<boolean> {
    try {
      console.log('🎤 Manually switching Azure account...');
      
      const wasTranscribing = this.isTranscribing;
      
      // 如果正在转录，先停止
      if (wasTranscribing) {
        await this.stopTranscription();
      }
      
      // 切换账号
      const switched = await this.accountManager.switchAccount();
      if (!switched) {
        console.error('🎤❌ Failed to switch account');
        return false;
      }
      
      // 更新当前账号
      this.currentAccount = this.accountManager.getCurrentAccount();
      
      // 如果之前在转录，重新开始
      if (wasTranscribing && this.currentAccount) {
        await this.startTranscription();
      }
      
      console.log(`🎤✅ Switched to account: ${this.currentAccount?.name}`);
      return true;
    } catch (error) {
      console.error('🎤❌ Error switching account:', error);
      this.emitError(error);
      return false;
    }
  }

  /**
   * 获取账号状态信息
   */
  getAccountStatus() {
    return {
      currentAccount: this.currentAccount?.name || 'None',
      remainingMinutes: this.accountManager.getCurrentAccountRemainingMinutes(),
      totalRemainingMinutes: this.accountManager.getTotalRemainingMinutes(),
      activeAccounts: this.accountManager.getActiveAccounts().length
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 设置账号管理器事件监听
   */
  private setupAccountManagerEvents(): void {
    this.accountManager.on('account-switched', async (data) => {
      console.log(`🎤 Account switched: ${data.newAccount.name}`);
      this.currentAccount = data.newAccount;
      
      // 如果正在转录，需要重新连接
      if (this.isTranscribing) {
        await this.reconnectWithNewAccount();
      }
      
      this.emit('account-changed', data);
    });
    
    this.accountManager.on('no-accounts-available', () => {
      console.error('🎤❌ No Azure accounts available');
      this.emitError(new Error('No Azure accounts available'));
      this.stopTranscription();
    });
    
    this.accountManager.on('usage-updated', (data) => {
      this.emit('usage-updated', data);
    });
    
    this.accountManager.on('error', (error) => {
      console.error('🎤❌ Account manager error:', error);
      this.emitError(error);
    });
  }

  /**
   * 使用新账号重新连接
   */
  private async reconnectWithNewAccount(): Promise<void> {
    try {
      console.log('🎤 Reconnecting with new account...');

      // 关闭当前连接
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      this.isConnected = false;

      // 使用新账号创建连接
      const success = await this.createWebSocketConnection();
      if (!success) {
        throw new Error('Failed to reconnect with new account');
      }

      console.log('🎤✅ Reconnected with new account');
    } catch (error) {
      console.error('🎤❌ Failed to reconnect with new account:', error);
      this.handleError(error);
    }
  }

  /**
   * 创建WebSocket连接
   */
  private async createWebSocketConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (!this.currentAccount) {
          console.error('🎤❌ No current account available');
          resolve(false);
          return;
        }

        // 生成连接ID
        this.connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 构建WebSocket URL - 使用正确的Azure Speech WebSocket格式
        const region = this.currentAccount.azureCredentials.region;
        const language = this.getAzureLanguageCode(this.config.language || 'zh-CN');

        // Azure Speech WebSocket的正确格式
        const wsUrl = `wss://${region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1` +
          `?language=${language}` +
          `&format=simple` +
          `&profanity=masked`;

        console.log(`🎤 Connecting to Azure Speech: ${wsUrl}`);
        console.log(`🎤 Using region: ${region}`);
        console.log(`🎤 Using language: ${language}`);

        // 检查token格式
        const token = this.currentAccount.azureCredentials.token;
        if (!token || token.length < 50) {
          console.error('🎤❌ Invalid token format');
          resolve(false);
          return;
        }

        console.log(`🎤 Token length: ${token.length}`);
        console.log(`🎤 Token preview: ${token.substring(0, 50)}...`);

        this.ws = new WebSocket(wsUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-ConnectionId': this.connectionId
          }
        });

        let hasResolved = false;

        this.ws.on('open', () => {
          console.log('🎤 Azure WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // 发送配置消息
          this.sendConfigurationMessage();

          if (!hasResolved) {
            hasResolved = true;
            resolve(true);
          }
        });

        this.ws.on('message', (data) => {
          this.handleWebSocketMessage(data);
        });

        this.ws.on('close', (code, reason) => {
          console.log(`🎤 Azure WebSocket closed: ${code} - ${reason}`);
          this.isConnected = false;

          if (this.isTranscribing) {
            this.handleConnectionLoss();
          }
        });

        this.ws.on('error', (error) => {
          console.error('🎤❌ Azure WebSocket error:', error);
          this.isConnected = false;

          // 分析错误类型
          const errorMessage = error.message || error.toString();
          const errorCode = (error as any).code;

          if (errorCode === 'ECONNRESET' || errorMessage.includes('network socket disconnected') || errorMessage.includes('TLS connection')) {
            console.log('🎤🌐 Network connection issue detected - attempting token refresh and retry...');

            // 网络连接问题，可能是token过期导致的，尝试刷新token
            if (this.currentAccount) {
              setTimeout(async () => {
                console.log('🎤🔄 Refreshing token due to network error...');
                const refreshed = await this.accountManager.refreshAccountToken(this.currentAccount!.id);

                if (refreshed) {
                  console.log('🎤✅ Token refreshed, retrying connection...');
                  this.currentAccount = this.accountManager.getCurrentAccount();

                  // 延迟重试连接
                  setTimeout(async () => {
                    const retrySuccess = await this.createWebSocketConnection();
                    if (retrySuccess) {
                      console.log('🎤🎉 Reconnected successfully after token refresh!');
                      if (this.isTranscribing) {
                        await this.startTranscription();
                      }
                    }
                  }, 3000);
                } else {
                  console.log('🎤⚠️ Token refresh failed, trying next account...');
                  const switched = await this.switchAccount();
                  if (switched) {
                    console.log('🎤🔄 Switched to new account, retrying...');
                    setTimeout(async () => {
                      const retrySuccess = await this.createWebSocketConnection();
                      if (retrySuccess && this.isTranscribing) {
                        await this.startTranscription();
                      }
                    }, 2000);
                  }
                }
              }, 1000);
            }
          } else if (errorMessage.includes('401')) {
            console.log('🎤🔑 Authentication failed - attempting automatic token refresh...');

            // 尝试刷新当前账号的token
            if (this.currentAccount) {
              setTimeout(async () => {
                console.log('🎤🔄 Refreshing token for current account...');
                const refreshed = await this.accountManager.refreshAccountToken(this.currentAccount!.id);

                if (refreshed) {
                  console.log('🎤✅ Token refreshed successfully, retrying connection...');
                  // 更新当前账号信息
                  this.currentAccount = this.accountManager.getCurrentAccount();

                  // 重试连接
                  setTimeout(async () => {
                    const retrySuccess = await this.createWebSocketConnection();
                    if (retrySuccess) {
                      console.log('🎤🎉 Reconnected successfully with refreshed token!');
                      // 如果重连成功，开始转录
                      if (this.isTranscribing) {
                        await this.startTranscription();
                      }
                    }
                  }, 2000);
                } else {
                  console.log('🎤⚠️ Token refresh failed, trying next account...');
                  // 尝试切换到下一个账号
                  const switched = await this.switchAccount();
                  if (switched) {
                    console.log('🎤🔄 Switched to new account, retrying...');
                    setTimeout(async () => {
                      const retrySuccess = await this.createWebSocketConnection();
                      if (retrySuccess && this.isTranscribing) {
                        await this.startTranscription();
                      }
                    }, 1000);
                  }
                }
              }, 1000);
            }
          } else if (errorMessage.includes('403')) {
            console.log('🎤⚠️ Access forbidden - quota may be exceeded');
          } else if (errorMessage.includes('429')) {
            console.log('🎤⚠️ Rate limit exceeded - will retry with backoff');
          }

          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }

          this.handleError(error);
        });

        // 设置连接超时
        setTimeout(() => {
          if (!hasResolved) {
            hasResolved = true;
            console.error('🎤❌ Azure WebSocket connection timeout');
            resolve(false);
          }
        }, this.config.timeout || 10000);

      } catch (error) {
        console.error('🎤❌ Failed to create Azure WebSocket connection:', error);
        resolve(false);
      }
    });
  }

  /**
   * 发送配置消息 - Azure Speech API格式
   */
  private sendConfigurationMessage(): void {
    if (!this.ws || !this.isConnected) return;

    try {
      // Azure Speech WebSocket需要特定的消息格式
      const timestamp = new Date().toISOString();
      const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 构建配置消息头
      const headers = [
        'Content-Type:application/json; charset=utf-8',
        `X-RequestId:${requestId}`,
        `X-Timestamp:${timestamp}`,
        'Path:speech.config'
      ].join('\r\n');

      // 配置消息体
      const configBody = JSON.stringify({
        context: {
          system: {
            version: "1.0.0"
          },
          os: {
            platform: "Node.js",
            name: "GeekAssistant",
            version: "1.0.0"
          },
          audio: {
            source: {
              connectivity: "Unknown",
              manufacturer: "GeekAssistant"
            }
          }
        }
      });

      // Azure格式：headers + \r\n\r\n + body
      const message = headers + '\r\n\r\n' + configBody;

      this.ws.send(message);
      console.log('🎤 Sent Azure configuration message');
    } catch (error) {
      console.error('🎤❌ Failed to send configuration message:', error);
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString());
      console.log('🎤📨 Azure message:', JSON.stringify(message, null, 2));

      // Azure Speech API的响应格式
      if (message.RecognitionStatus) {
        // 最终识别结果
        if (message.RecognitionStatus === 'Success') {
          const result: TranscriptionResult = {
            text: message.DisplayText || message.NBest?.[0]?.Display || '',
            isFinal: true,
            confidence: message.NBest?.[0]?.Confidence || 0.9,
            timestamp: Date.now(),
            language: this.config.language
          };

          if (result.text.trim()) {
            console.log('🎤✅ Final result:', result.text);
            this.emitTranscription(result);
          }
        } else if (message.RecognitionStatus === 'InitialSilenceTimeout') {
          console.log('🎤⏰ Initial silence timeout');
        } else if (message.RecognitionStatus === 'BabbleTimeout') {
          console.log('🎤⏰ Babble timeout');
        } else if (message.RecognitionStatus === 'Error') {
          console.error('🎤❌ Recognition error:', message.ErrorDetails);
          this.handleError(new Error(message.ErrorDetails || 'Recognition error'));
        }
      } else if (message.Text) {
        // 中间识别结果
        const result: TranscriptionResult = {
          text: message.Text,
          isFinal: false,
          confidence: 0.7,
          timestamp: Date.now(),
          language: this.config.language
        };

        if (result.text.trim()) {
          console.log('🎤🔄 Partial result:', result.text);
          this.emitTranscription(result);
        }
      } else {
        console.log('🎤📝 Unknown message format:', message);
      }
    } catch (error) {
      console.error('🎤❌ Failed to parse Azure WebSocket message:', error);
      console.log('🎤📄 Raw message:', data.toString());
    }
  }

  /**
   * 处理连接丢失
   */
  private handleConnectionLoss(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // 指数退避

      console.log(`🎤 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);

      this.reconnectTimeout = setTimeout(async () => {
        try {
          const success = await this.createWebSocketConnection();
          if (!success) {
            this.handleConnectionLoss();
          }
        } catch (error) {
          console.error('🎤❌ Reconnection failed:', error);
          this.handleConnectionLoss();
        }
      }, delay);
    } else {
      console.error('🎤❌ Max reconnection attempts reached');
      this.emitError(new Error('Connection lost and max reconnection attempts reached'));
      this.stopTranscription();
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: any): void {
    console.error('🎤❌ Azure transcription error:', error);
    this.updateStatus(TranscriptionStatus.ERROR);
    this.emitError(error);
  }

  /**
   * 获取Azure语言代码
   */
  private getAzureLanguageCode(language: string): string {
    const languageMap: { [key: string]: string } = {
      'zh-CN': 'zh-CN',
      'zh': 'zh-CN',
      'en-US': 'en-US',
      'en': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU'
    };

    return languageMap[language] || 'zh-CN';
  }
}
