import { TranscriptionProvider } from './ServiceTypes';

/**
 * 音频配置要求
 */
export interface AudioRequirements {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  encoding: 'pcm_s16le' | 'pcm_f32le' | 'opus' | 'mp3';
  bufferSize?: number;
}

/**
 * 语言映射配置
 */
export interface LanguageMapping {
  [systemLanguage: string]: string;
}

/**
 * 转录服务提供商适配器
 * 统一管理不同转录服务的配置差异
 */
export abstract class TranscriptionProviderAdapter {
  protected provider: TranscriptionProvider;

  constructor(provider: TranscriptionProvider) {
    this.provider = provider;
  }

  /**
   * 获取该服务商的音频要求
   */
  abstract getAudioRequirements(): AudioRequirements;

  /**
   * 获取语言代码映射
   */
  abstract getLanguageMapping(): LanguageMapping;

  /**
   * 将系统语言代码转换为服务商特定的语言代码
   */
  mapLanguage(systemLanguage: string): string {
    const mapping = this.getLanguageMapping();
    return mapping[systemLanguage] || mapping['en'] || 'en';
  }

  /**
   * 获取服务商特定的配置
   */
  abstract getProviderSpecificConfig(baseConfig: any): any;

  /**
   * 验证配置是否有效
   */
  abstract validateConfig(config: any): boolean;

  /**
   * 获取服务商信息
   */
  getProviderInfo() {
    return {
      provider: this.provider,
      audioRequirements: this.getAudioRequirements(),
      supportedLanguages: Object.keys(this.getLanguageMapping())
    };
  }
}

/**
 * Speechmatics 适配器
 */
export class SpeechmaticsAdapter extends TranscriptionProviderAdapter {
  constructor() {
    super(TranscriptionProvider.SPEECHMATICS);
  }

  getAudioRequirements(): AudioRequirements {
    return {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
      encoding: 'pcm_s16le',
      bufferSize: 1024
    };
  }

  getLanguageMapping(): LanguageMapping {
    return {
      'zh-CN': 'cmn',
      'zh': 'cmn',
      'en-US': 'en',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'es': 'es',
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt',
      'ru': 'ru'
    };
  }

  getProviderSpecificConfig(baseConfig: any) {
    return {
      ...baseConfig,
      language: this.mapLanguage(baseConfig.language || 'en'),
      sampleRate: this.getAudioRequirements().sampleRate
    };
  }

  validateConfig(config: any): boolean {
    return !!(config.apiKey && config.language);
  }
}

/**
 * Deepgram 适配器
 */
export class DeepgramAdapter extends TranscriptionProviderAdapter {
  constructor() {
    super(TranscriptionProvider.DEEPGRAM);
  }

  getAudioRequirements(): AudioRequirements {
    return {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
      encoding: 'pcm_s16le',
      bufferSize: 2048
    };
  }

  getLanguageMapping(): LanguageMapping {
    return {
      'zh-CN': 'zh-CN',
      'zh': 'zh',
      'en-US': 'en-US',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'es': 'es',
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt',
      'ru': 'ru'
    };
  }

  getProviderSpecificConfig(baseConfig: any) {
    return {
      ...baseConfig,
      language: this.mapLanguage(baseConfig.language || 'en'),
      model: baseConfig.model || 'nova-2',
      smartFormat: true,
      punctuate: baseConfig.punctuation !== false,
      interim_results: true
    };
  }

  validateConfig(config: any): boolean {
    return !!(config.apiKey);
  }
}

/**
 * Gladia 适配器
 */
export class GladiaAdapter extends TranscriptionProviderAdapter {
  constructor() {
    super(TranscriptionProvider.GLADIA);
  }

  getAudioRequirements(): AudioRequirements {
    return {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
      encoding: 'pcm_s16le',
      bufferSize: 1024
    };
  }

  getLanguageMapping(): LanguageMapping {
    return {
      'zh-CN': 'zh',
      'zh': 'zh',
      'en-US': 'en',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'es': 'es',
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt',
      'ru': 'ru'
    };
  }

  getProviderSpecificConfig(baseConfig: any) {
    return {
      ...baseConfig,
      language: this.mapLanguage(baseConfig.language || 'en'),
      model: baseConfig.model || 'whisper',
      punctuation: baseConfig.punctuation !== false,
      realtime: baseConfig.realtime !== false
    };
  }

  validateConfig(config: any): boolean {
    return !!(config.apiKey);
  }
}

/**
 * AssemblyAI 适配器
 */
export class AssemblyAIAdapter extends TranscriptionProviderAdapter {
  constructor() {
    super(TranscriptionProvider.ASSEMBLYAI);
  }

  getAudioRequirements(): AudioRequirements {
    return {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
      encoding: 'pcm_s16le',
      bufferSize: 2048
    };
  }

  getLanguageMapping(): LanguageMapping {
    return {
      'zh-CN': 'zh',
      'zh': 'zh',
      'en-US': 'en',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'es': 'es',
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt',
      'ru': 'ru'
    };
  }

  getProviderSpecificConfig(baseConfig: any) {
    return {
      ...baseConfig,
      language: this.mapLanguage(baseConfig.language || 'en'),
      punctuation: baseConfig.punctuation !== false,
      realtime: baseConfig.realtime !== false
    };
  }

  validateConfig(config: any): boolean {
    return !!(config.apiKey);
  }
}

/**
 * Azure 适配器
 */
export class AzureAdapter extends TranscriptionProviderAdapter {
  constructor() {
    super(TranscriptionProvider.AZURE);
  }

  getAudioRequirements(): AudioRequirements {
    return {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
      encoding: 'pcm_s16le',
      bufferSize: 1024
    };
  }

  getLanguageMapping(): LanguageMapping {
    return {
      'zh-CN': 'zh-CN',
      'zh': 'zh-CN',
      'en-US': 'en-US',
      'en': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU'
    };
  }

  getProviderSpecificConfig(baseConfig: any) {
    return {
      ...baseConfig,
      language: this.mapLanguage(baseConfig.language || 'zh-CN'),
      recognitionMode: baseConfig.recognitionMode || 'conversation',
      profanityOption: baseConfig.profanityOption || 'masked',
      outputFormat: baseConfig.outputFormat || 'simple',
      punctuate: baseConfig.punctuation !== false,
      realtime: baseConfig.realtime !== false,
      sampleRate: this.getAudioRequirements().sampleRate,
      channels: this.getAudioRequirements().channels
    };
  }
}

/**
 * 适配器工厂
 */
export class TranscriptionAdapterFactory {
  private static adapters: Map<TranscriptionProvider, TranscriptionProviderAdapter> = new Map();

  static getAdapter(provider: TranscriptionProvider): TranscriptionProviderAdapter {
    if (!this.adapters.has(provider)) {
      let adapter: TranscriptionProviderAdapter;

      switch (provider) {
        case TranscriptionProvider.SPEECHMATICS:
          adapter = new SpeechmaticsAdapter();
          break;
        case TranscriptionProvider.DEEPGRAM:
          adapter = new DeepgramAdapter();
          break;
        case TranscriptionProvider.GLADIA:
          adapter = new GladiaAdapter();
          break;
        case TranscriptionProvider.ASSEMBLYAI:
          adapter = new AssemblyAIAdapter();
          break;
        case TranscriptionProvider.AZURE:
          adapter = new AzureAdapter();
          break;
        default:
          throw new Error(`Unsupported transcription provider: ${provider}`);
      }

      this.adapters.set(provider, adapter);
    }

    return this.adapters.get(provider)!;
  }

  static getAllAdapters(): TranscriptionProviderAdapter[] {
    return [
      this.getAdapter(TranscriptionProvider.SPEECHMATICS),
      this.getAdapter(TranscriptionProvider.DEEPGRAM),
      this.getAdapter(TranscriptionProvider.GLADIA),
      this.getAdapter(TranscriptionProvider.ASSEMBLYAI),
      this.getAdapter(TranscriptionProvider.AZURE)
    ];
  }
}
