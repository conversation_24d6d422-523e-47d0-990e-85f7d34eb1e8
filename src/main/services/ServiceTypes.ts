export enum ServiceMode {
  GEMINI_LIVE = 'gemini-live',
  SEPARATED = 'separated'
}

export enum TranscriptionProvider {
  ASSEMBLYAI = 'assemblyai',
  DEEPGRAM = 'deepgram',
  OPENAI = 'openai',
  XUNFEI = 'xunfei',
  TENCENT = 'tencent',
  GEMINI = 'gemini',
  SPEECHMATICS = 'speechmatics',
  GLADIA = 'gladia',
  AZURE = 'azure'
}

export enum AIProvider {
  GROQ = 'groq',
  OPENAI = 'openai',
  OLLAMA = 'ollama',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  TOGETHER = 'together',
  CUSTOM = 'custom'
}

export enum ServiceEvent {
  STATUS_CHANGED = 'status-changed',
  TRANSCRIPTION_RECEIVED = 'transcription-received',
  AI_RESPONSE_RECEIVED = 'ai-response-received',
  ERROR_OCCURRED = 'error-occurred',
  METRICS_UPDATED = 'metrics-updated'
}

export interface ServiceConfig {
  mode: ServiceMode;
  geminiLive?: {
    apiKey: string;
    model?: string;
    language?: string;
    customPrompt?: string;
    profile?: string;
    retryAttempts?: number;
  };
  separated?: {
    transcription: {
      provider: TranscriptionProvider;
      config: {
        apiKey: string;
        language?: string;
        model?: string;
        realtime?: boolean;
        punctuation?: boolean;
        timeout?: number;
        retryAttempts?: number;
      };
    };
    ai: {
      provider: AIProvider;
      config: {
        apiKey: string;
        model?: string;
        temperature?: number;
        maxTokens?: number;
        timeout?: number;
        retryAttempts?: number;
      };
    };
  };
}
