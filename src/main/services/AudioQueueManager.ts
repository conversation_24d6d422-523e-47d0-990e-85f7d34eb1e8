import { EventEmitter } from 'events'

/**
 * 高效音频队列管理器
 * 实现生产者-消费者模式，保证音频流的稳定性和实时性
 */
export class AudioQueueManager extends EventEmitter {
  private queue: Buffer[] = []
  private isProcessing = false
  private maxQueueSize: number
  private processingInterval: NodeJS.Timeout | null = null
  private emptyQueueCount = 0
  private stats = {
    produced: 0,
    consumed: 0,
    dropped: 0,
    maxQueueLength: 0
  }

  constructor(maxQueueSize = 100) {
    super()
    this.maxQueueSize = maxQueueSize
  }

  /**
   * 生产者：添加音频数据到队列
   */
  enqueue(audioData: Buffer): boolean {
    if (this.queue.length >= this.maxQueueSize) {
      // 队列满了，丢弃最老的数据（背压控制）
      const dropped = this.queue.shift()
      this.stats.dropped++
      console.warn(`🎵⚠️ Audio queue full, dropped ${dropped?.length} bytes`)
    }

    this.queue.push(audioData)
    this.stats.produced++
    this.stats.maxQueueLength = Math.max(this.stats.maxQueueLength, this.queue.length)

    // 如果队列不为空且没在处理，启动消费者
    if (!this.isProcessing) {
      this.startProcessing()
    }

    return true
  }

  /**
   * 启动消费者处理
   */
  private startProcessing(): void {
    if (this.isProcessing) return

    this.isProcessing = true
    // 只在第一次启动时输出日志
    if (this.stats.produced === 1) {
      console.log('🎵🚀 Audio queue processing started')
    }

    // 使用 setImmediate 确保异步处理，不阻塞音频捕获
    this.processingInterval = setInterval(() => {
      this.processQueue()
    }, 5) // 5ms 间隔，提升实时性
  }

  /**
   * 消费者：处理队列中的音频数据
   */
  private processQueue(): void {
    if (this.queue.length === 0) {
      // 不立即停止，等待更多数据
      return
    }

    // 批量处理，优化实时性和API效率的平衡
    const batchSize = Math.min(15, this.queue.length) // 增加批量大小以减少API调用频率
    const batch: Buffer[] = []

    for (let i = 0; i < batchSize; i++) {
      const audioData = this.queue.shift()
      if (audioData) {
        batch.push(audioData)
        this.stats.consumed++
      }
    }

    if (batch.length > 0) {
      // 发送批量数据给处理器
      this.emit('audio-batch', batch)
    }

    // 如果队列持续为空超过一定时间，才停止处理
    if (this.queue.length === 0) {
      this.emptyQueueCount = (this.emptyQueueCount || 0) + 1
      if (this.emptyQueueCount > 200) { // 1秒后停止 (5ms * 200)
        this.stopProcessing()
        this.emptyQueueCount = 0
      }
    } else {
      this.emptyQueueCount = 0
    }
  }

  /**
   * 停止消费者处理
   */
  private stopProcessing(): void {
    if (!this.isProcessing) return

    this.isProcessing = false
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
    // 减少日志输出频率
    if (this.stats.consumed % 100 === 0) {
      console.log('🎵⏸️ Audio queue processing paused (no data)')
    }
  }

  /**
   * 获取队列状态
   */
  getStats() {
    return {
      ...this.stats,
      currentQueueLength: this.queue.length,
      isProcessing: this.isProcessing,
      queueUtilization: (this.queue.length / this.maxQueueSize * 100).toFixed(1) + '%'
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue = []
    this.stopProcessing()
    console.log('🎵🧹 Audio queue cleared')
  }

  /**
   * 销毁队列管理器
   */
  destroy(): void {
    this.clear()
    this.removeAllListeners()
    console.log('🎵💥 Audio queue manager destroyed')
  }

  /**
   * 检查队列健康状态
   */
  isHealthy(): boolean {
    const utilizationPercent = (this.queue.length / this.maxQueueSize) * 100
    return utilizationPercent < 80 // 队列使用率低于80%认为健康
  }

  /**
   * 获取队列延迟估算（毫秒）
   */
  getEstimatedLatency(): number {
    // 假设每个音频块代表约10-50ms的音频
    // 这是一个粗略估算
    return this.queue.length * 25 // 平均25ms per chunk
  }
}

/**
 * 音频处理器接口
 */
export interface AudioProcessor {
  processAudio(audioData: Buffer): Promise<void>
}

/**
 * 音频流处理器
 * 集成队列管理和音频处理逻辑
 */
export class AudioStreamProcessor extends EventEmitter {
  private queueManager: AudioQueueManager
  private processors: AudioProcessor[] = []
  private isActive = false
  private audioBuffer: Buffer = Buffer.alloc(0)
  private minBufferSize: number = 0

  constructor(maxQueueSize = 100) {
    super()
    this.queueManager = new AudioQueueManager(maxQueueSize)

    // 计算最小缓冲区大小（优化为更小的块以降低延迟）
    // SystemAudioDump: 24kHz, 2声道, 16位 = 24000 * 2 * 2 = 96000 bytes/秒
    // 重采样后: 16kHz, 1声道, 16位 = 16000 * 1 * 2 = 32000 bytes/秒
    // 目标：重采样后25ms音频块，原始数据需要37.5ms: 96000 * 0.0375 = 3600 bytes
    this.minBufferSize = 3600 // 37.5ms的原始音频数据，重采样后约25ms

    // 监听队列的批量音频数据
    this.queueManager.on('audio-batch', this.handleAudioBatch.bind(this))
  }

  /**
   * 添加音频处理器
   */
  addProcessor(processor: AudioProcessor): void {
    this.processors.push(processor)
    console.log(`🎵➕ Audio processor added, total: ${this.processors.length}`)
  }

  /**
   * 移除音频处理器
   */
  removeProcessor(processor: AudioProcessor): void {
    const index = this.processors.indexOf(processor)
    if (index > -1) {
      this.processors.splice(index, 1)
      console.log(`🎵➖ Audio processor removed, total: ${this.processors.length}`)
    }
  }

  /**
   * 启动音频流处理
   */
  start(): void {
    this.isActive = true
    console.log('🎵🎬 Audio stream processor started')
  }

  /**
   * 停止音频流处理
   */
  stop(): void {
    this.isActive = false
    this.queueManager.clear()
    this.audioBuffer = Buffer.alloc(0) // 清空音频缓冲区
    console.log('🎵🛑 Audio stream processor stopped')
  }

  /**
   * 接收音频数据（生产者接口）
   */
  receiveAudio(audioData: Buffer): boolean {
    if (!this.isActive) {
      return false
    }

    return this.queueManager.enqueue(audioData)
  }

  /**
   * 处理批量音频数据
   */
  private async handleAudioBatch(batch: Buffer[]): Promise<void> {
    if (!this.isActive || this.processors.length === 0) {
      return
    }

    try {
      // 合并批量数据到缓冲区
      const combinedBuffer = Buffer.concat(batch)
      this.audioBuffer = Buffer.concat([this.audioBuffer, combinedBuffer])

      // 只有当缓冲区足够大时才发送（确保重采样后至少50ms音频）
      while (this.audioBuffer.length >= this.minBufferSize) {
        // 取出足够的数据（约100ms原始数据，减少延迟）
        const chunkSize = Math.min(this.minBufferSize * 1.5, this.audioBuffer.length) // 100ms原始数据
        const audioChunk = this.audioBuffer.slice(0, chunkSize)
        this.audioBuffer = this.audioBuffer.slice(chunkSize)

        // 调试日志：只在开发模式下偶尔显示
        if (process.env.NODE_ENV === 'development' && Math.random() < 0.01) {
          const durationMs = (chunkSize / (16000 * 2)) * 1000 // 16kHz, 16位
          console.log(`🎵📤 Sending audio chunk: ${chunkSize} bytes, ~${durationMs.toFixed(1)}ms`)
        }

        // 并行发送给所有处理器
        const promises = this.processors.map(processor =>
          processor.processAudio(audioChunk).catch(error => {
            console.error('🎵❌ Audio processor error:', error)
            this.emit('processor-error', error)
          })
        )

        await Promise.allSettled(promises)
      }
    } catch (error) {
      console.error('🎵❌ Audio batch processing error:', error)
      this.emit('processing-error', error)
    }
  }

  /**
   * 获取处理器状态
   */
  getStatus() {
    return {
      isActive: this.isActive,
      processorCount: this.processors.length,
      queueStats: this.queueManager.getStats(),
      isHealthy: this.queueManager.isHealthy(),
      estimatedLatency: this.queueManager.getEstimatedLatency()
    }
  }

  /**
   * 销毁处理器
   */
  destroy(): void {
    this.stop()
    this.queueManager.destroy()
    this.processors = []
    this.removeAllListeners()
    console.log('🎵💥 Audio stream processor destroyed')
  }
}
