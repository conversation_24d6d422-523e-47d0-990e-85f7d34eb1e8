import { <PERSON>rowserWindow } from 'electron';
import { GoogleGenAI } from '@google/genai';
import { ServiceAdapter, AdapterStatus } from './ServiceAdapter';
import { ServiceConfig } from './ServiceTypes';

/**
 * Gemini Live API 适配器
 * 提供实时语音交互功能
 */
export class GeminiLiveAdapter extends ServiceAdapter {
  private geminiSession: any = null;
  private isInitializingSession = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  // 状态变量
  private currentApiKey = '';
  private currentCustomPrompt = '';
  private currentProfile = 'interview';
  private currentLanguage = 'cmn-CN';
  private messageBuffer = '';
  private currentTranscription = '';

  constructor(config: ServiceConfig) {
    super(config);
    console.log('🎤 GeminiLiveAdapter created with config:', config.mode);
  }

  /**
   * 初始化Gemini Live服务
   */
  async initialize(config?: ServiceConfig): Promise<boolean> {
    try {
      if (config) {
        this.config = config;
      }

      if (!this.config.geminiLive?.apiKey) {
        this.handleError('Gemini API key is required');
        return false;
      }

      console.log('🎤 Initializing Gemini Live service...');
      this.updateStatus(AdapterStatus.CONNECTING);

      const success = await this.initializeGeminiSession(
        this.config.geminiLive.apiKey,
        this.config.geminiLive.customPrompt || '',
        this.config.geminiLive.profile || 'interview',
        this.config.geminiLive.language || 'cmn-CN'
      );

      if (success) {
        this.isInitialized = true;
        this.updateStatus(AdapterStatus.CONNECTED);
        console.log('🎤✅ GeminiLiveAdapter initialized successfully');
      } else {
        this.updateStatus(AdapterStatus.ERROR);
      }

      return success;
    } catch (error) {
      console.error('🎤❌ Failed to initialize GeminiLiveAdapter:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 启动会话
   */
  async startSession(config?: any): Promise<boolean> {
    try {
      if (!this.isReady()) {
        console.warn('🎤 GeminiLiveAdapter not ready, attempting to initialize...');
        if (!await this.initialize()) {
          return false;
        }
      }

      console.log('🎤✅ GeminiLiveAdapter session started');
      this.emitServiceEvent('session-started', { mode: 'gemini-live' });
      return true;
    } catch (error) {
      console.error('🎤❌ Failed to start Gemini Live session:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止会话
   */
  async stopSession(): Promise<void> {
    if (this.geminiSession) {
      try {
        this.geminiSession.close();
        this.geminiSession = null;
        console.log('Gemini session stopped');
      } catch (error) {
        console.error('Error stopping Gemini session:', error);
      }
    }

    this.clearReconnectTimeout();
    this.updateStatus(AdapterStatus.DISCONNECTED);
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.geminiSession) {
      console.warn('Gemini session not available, skipping audio data');
      return;
    }

    try {
      await this.geminiSession.sendRealtimeInput({
        audio: {
          data: audioData,
          mimeType: 'audio/pcm;rate=24000'
        }
      });
    } catch (error) {
      console.error('Error sending audio to Gemini:', error);
      this.handleError(error);
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    console.log('Manual disconnect requested');
    
    if (this.geminiSession) {
      this.geminiSession.close();
      this.geminiSession = null;
    }

    this.clearReconnectTimeout();
    this.reconnectAttempts = this.maxReconnectAttempts;
    this.updateStatus(AdapterStatus.DISCONNECTED);
    this.sendToRenderer('session-closed');
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<boolean> {
    console.log('Manual reconnect requested');
    this.clearReconnectTimeout();
    this.reconnectAttempts = 0;

    if (this.currentApiKey) {
      const success = await this.initializeGeminiSession(
        this.currentApiKey,
        this.currentCustomPrompt,
        this.currentProfile,
        this.currentLanguage
      );

      if (success) {
        this.sendToRenderer('update-status', 'Manual reconnect successful');
        this.updateStatus(AdapterStatus.CONNECTED);
      }

      return success;
    }

    return false;
  }

  /**
   * 检查是否准备就绪
   */
  isReady(): boolean {
    return this.isInitialized && this.status === AdapterStatus.CONNECTED;
  }





  /**
   * 初始化Gemini会话
   */
  private async initializeGeminiSession(
    apiKey: string,
    customPrompt = '',
    profile = 'interview',
    language = 'cmn-CN'
  ): Promise<boolean> {
    if (this.isInitializingSession) {
      console.log('Session initialization already in progress');
      return false;
    }

    console.log('Initializing Gemini session with:', {
      apiKeyLength: apiKey?.length || 0,
      profile,
      language,
      customPromptLength: customPrompt?.length || 0
    });

    if (!apiKey || apiKey.trim() === '') {
      console.error('Invalid API key provided');
      this.sendToRenderer('session-error', 'API密钥无效或为空');
      return false;
    }

    this.currentApiKey = apiKey;
    this.currentCustomPrompt = customPrompt;
    this.currentProfile = profile;
    this.currentLanguage = language;
    this.isInitializingSession = true;

    this.sendToRenderer('session-initializing', true);
    this.clearReconnectTimeout();

    try {
      console.log('Creating GoogleGenAI client...');
      const genAI = new GoogleGenAI({
        apiKey: apiKey
      });

      const systemPrompt = this.generateSystemPrompt(profile, customPrompt);
      console.log('System prompt generated, length:', systemPrompt.length);

      console.log('Connecting to Gemini Live API...');
      const session = await genAI.live.connect({
        model: 'gemini-live-2.5-flash-preview',
        callbacks: {
          onopen: () => {
            console.log('Gemini session opened successfully');
            this.reconnectAttempts = 0;
            this.sendToRenderer('update-status', 'Connected to Gemini - Starting recording...');
          },
          onmessage: (message) => {
            this.handleGeminiMessage(message);
          },
          onerror: (error) => {
            this.handleGeminiError(error);
          },
          onclose: (event) => {
            this.handleGeminiClose(event);
          }
        },
        config: {
          responseModalities: ['text' as any],
          inputAudioTranscription: {},
          systemInstruction: {
            parts: [{ text: systemPrompt }]
          }
        }
      });

      this.geminiSession = session;
      this.isInitializingSession = false;
      this.sendToRenderer('session-initializing', false);

      return true;
    } catch (error) {
      console.error('Failed to initialize Gemini session:', error);
      this.isInitializingSession = false;
      this.sendToRenderer('session-initializing', false);
      this.sendToRenderer('session-error', `连接失败: ${error}`);
      return false;
    }
  }

  /**
   * 生成系统提示词
   */
  private generateSystemPrompt(profile: string, customPrompt: string): string {
    // 这里应该根据profile生成相应的系统提示词
    // 暂时返回一个基础的面试助手提示词
    let basePrompt = `你是一个专业的面试助手，专门帮助候选人在面试中提供智能回答建议。

核心原则：
1. 提供简洁、专业、真实的回答建议
2. 回答要符合中国职场文化和习惯
3. 避免过度夸张或虚假的表述
4. 重点突出候选人的能力和经验
5. 保持积极正面的态度

回答格式：
- 直接给出可以说的回答内容
- 语言自然流畅，适合口语表达
- 控制在30-60秒的说话时长
- 重点突出，逻辑清晰`;

    if (customPrompt) {
      basePrompt += `\n\n自定义要求：\n${customPrompt}`;
    }

    return basePrompt;
  }

  /**
   * 处理Gemini消息
   */
  private handleGeminiMessage(message: any): void {
    try {
      // 处理转录和AI回复
      if (message.inputAudioTranscription) {
        const transcription = message.inputAudioTranscription.transcript;
        if (transcription) {
          console.log('🎤 Gemini transcription:', transcription);
          this.handleTranscription({
            text: transcription,
            isFinal: true,
            timestamp: Date.now()
          });
        }
      }

      if (message.text) {
        console.log('🎤 Gemini AI response:', message.text);
        this.handleAIResponse({
          text: message.text,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('🎤❌ Error handling Gemini message:', error);
    }
  }

  /**
   * 处理Gemini错误
   */
  private handleGeminiError(error: any): void {
    console.error('Gemini session error:', error);
    this.sendToRenderer('session-error', `会话错误: ${error}`);
    this.handleError(error);
  }

  /**
   * 处理Gemini连接关闭
   */
  private handleGeminiClose(event: any): void {
    console.log('Gemini session closed:', event);
    this.geminiSession = null;
    this.updateStatus(AdapterStatus.DISCONNECTED);
    this.sendToRenderer('session-closed');
  }

  /**
   * 清除重连超时
   */
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopSession();
    this.removeAllListeners();
  }
}
