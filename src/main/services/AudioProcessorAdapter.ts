import { AudioProcessor } from './AudioQueueManager'
import { ServiceManager } from './ServiceManager'
import { EventEmitter } from 'events'

/**
 * 音频重采样处理器
 * 处理SystemAudioDump的24kHz立体声输出，转换为API要求的格式
 */
export class AudioResamplerProcessor extends EventEmitter implements AudioProcessor {
  private sourceSampleRate: number
  private targetSampleRate: number
  private sourceChannels: number
  private targetChannels: number
  private nextProcessor: AudioProcessor | null = null

  constructor(
    sourceSampleRate: number = 24000,
    targetSampleRate: number = 16000,
    sourceChannels: number = 2,
    targetChannels: number = 1
  ) {
    super()
    this.sourceSampleRate = sourceSampleRate
    this.targetSampleRate = targetSampleRate
    this.sourceChannels = sourceChannels
    this.targetChannels = targetChannels
  }

  setNextProcessor(processor: AudioProcessor): void {
    this.nextProcessor = processor
  }

  async processAudio(audioData: Buffer): Promise<void> {
    try {
      let processedData = audioData

      // 转换立体声到单声道（如果需要）
      if (this.sourceChannels === 2 && this.targetChannels === 1) {
        processedData = this.stereoToMono(processedData)
      }

      // 重采样（如果需要）
      if (this.sourceSampleRate !== this.targetSampleRate) {
        processedData = this.resampleAudio(processedData, this.sourceSampleRate, this.targetSampleRate)
      }

      // 如果有下一个处理器，传递给它
      if (this.nextProcessor) {
        await this.nextProcessor.processAudio(processedData)
      }

      // 发送处理后的音频数据事件
      this.emit('processed-audio', processedData)
    } catch (error) {
      console.error('🎵❌ Audio resampler error:', error)
      throw error
    }
  }

  /**
   * 立体声转单声道
   */
  private stereoToMono(stereoBuffer: Buffer): Buffer {
    const monoBuffer = Buffer.alloc(stereoBuffer.length / 2)

    for (let i = 0; i < stereoBuffer.length; i += 4) {
      // 读取左右声道的16位样本
      const left = stereoBuffer.readInt16LE(i)
      const right = stereoBuffer.readInt16LE(i + 2)

      // 平均值转换为单声道
      const mono = Math.round((left + right) / 2)

      // 写入单声道缓冲区
      monoBuffer.writeInt16LE(mono, i / 2)
    }

    return monoBuffer
  }

  /**
   * 音频重采样（改进的算法）
   */
  private resampleAudio(inputBuffer: Buffer, inputSampleRate: number, outputSampleRate: number): Buffer {
    if (inputSampleRate === outputSampleRate) {
      return inputBuffer
    }

    const inputSamples = inputBuffer.length / 2 // 16位样本
    const outputSamples = Math.floor(inputSamples * outputSampleRate / inputSampleRate)
    const outputBuffer = Buffer.alloc(outputSamples * 2)

    const ratio = inputSamples / outputSamples

    for (let i = 0; i < outputSamples; i++) {
      const inputIndex = i * ratio
      const inputIndexFloor = Math.floor(inputIndex)
      const inputIndexCeil = Math.min(inputIndexFloor + 1, inputSamples - 1)

      const sample1 = inputBuffer.readInt16LE(inputIndexFloor * 2)
      const sample2 = inputBuffer.readInt16LE(inputIndexCeil * 2)

      // 线性插值
      const fraction = inputIndex - inputIndexFloor
      const interpolatedSample = Math.round(sample1 + (sample2 - sample1) * fraction)

      outputBuffer.writeInt16LE(interpolatedSample, i * 2)
    }

    return outputBuffer
  }


}

/**
 * 服务管理器音频处理器
 * 将处理后的音频发送给转录服务
 */
export class ServiceManagerAudioProcessor implements AudioProcessor {
  private serviceManager: ServiceManager | null = null

  constructor(serviceManager: ServiceManager) {
    this.serviceManager = serviceManager
  }

  async processAudio(audioData: Buffer): Promise<void> {
    if (!this.serviceManager) {
      console.warn('🎵⚠️ ServiceManager not available, skipping audio')
      return
    }

    try {
      await this.serviceManager.sendAudio(audioData)
    } catch (error) {
      console.error('🎵❌ Failed to send audio to service manager:', error)
      throw error
    }
  }
}

/**
 * 音频分析处理器
 * 用于调试和监控音频质量
 */
export class AudioAnalysisProcessor implements AudioProcessor {
  private analysisInterval: number
  private lastAnalysisTime: number = 0

  constructor(analysisInterval: number = 10000) { // 10秒分析一次，减少日志
    this.analysisInterval = analysisInterval
  }

  async processAudio(audioData: Buffer): Promise<void> {
    const now = Date.now()
    
    // 限制分析频率，避免性能影响
    if (now - this.lastAnalysisTime < this.analysisInterval) {
      return
    }

    this.lastAnalysisTime = now

    try {
      const analysis = this.analyzeAudioBuffer(audioData)
      
      // 只在音频质量有问题时输出警告
      if (analysis.silencePercentage > 80) {
        console.warn(`🎵⚠️ High silence detected: ${analysis.silencePercentage.toFixed(1)}%, RMS: ${analysis.rmsValue.toFixed(2)}`)
      } else if (analysis.silencePercentage < 50) {
        console.log(`🎵✅ Good audio quality: Silence: ${analysis.silencePercentage.toFixed(1)}%, RMS: ${analysis.rmsValue.toFixed(2)}`)
      }
    } catch (error) {
      console.error('🎵❌ Audio analysis error:', error)
    }
  }

  /**
   * 分析音频缓冲区
   */
  private analyzeAudioBuffer(buffer: Buffer) {
    const int16Array = new Int16Array(buffer.buffer, buffer.byteOffset, buffer.length / 2)

    let minValue = 32767
    let maxValue = -32768
    let avgValue = 0
    let rmsValue = 0
    let silentSamples = 0

    for (let i = 0; i < int16Array.length; i++) {
      const sample = int16Array[i]
      minValue = Math.min(minValue, sample)
      maxValue = Math.max(maxValue, sample)
      avgValue += sample
      rmsValue += sample * sample

      if (Math.abs(sample) < 100) {
        silentSamples++
      }
    }

    avgValue /= int16Array.length
    rmsValue = Math.sqrt(rmsValue / int16Array.length)

    const silencePercentage = (silentSamples / int16Array.length) * 100

    return {
      minValue,
      maxValue,
      avgValue,
      rmsValue,
      silencePercentage,
      sampleCount: int16Array.length,
    }
  }
}

/**
 * 复合音频处理器
 * 将多个处理器串联起来
 */
export class CompositeAudioProcessor implements AudioProcessor {
  private processors: AudioProcessor[] = []

  constructor(processors: AudioProcessor[] = []) {
    this.processors = processors
  }

  addProcessor(processor: AudioProcessor): void {
    this.processors.push(processor)
  }

  removeProcessor(processor: AudioProcessor): void {
    const index = this.processors.indexOf(processor)
    if (index > -1) {
      this.processors.splice(index, 1)
    }
  }

  async processAudio(audioData: Buffer): Promise<void> {
    let currentData = audioData

    // 串行处理每个处理器
    for (const processor of this.processors) {
      try {
        await processor.processAudio(currentData)
        // 注意：这里假设处理器不修改原始数据
        // 如果需要链式处理，需要修改接口设计
      } catch (error) {
        console.error('🎵❌ Composite processor error:', error)
        // 继续处理其他处理器，不因一个失败而全部停止
      }
    }
  }
}
