import { promises as fs } from 'fs';
import { join } from 'path';
import { app } from 'electron';
import { ServiceConfig, ServiceMode, TranscriptionProvider, AIProvider } from './ServiceTypes';

// 默认API密钥配置
const DEFAULT_API_KEYS = {
  gemini: process.env.GEMINI_API_KEY || '',
  deepgram: process.env.DEEPGRAM_API_KEY || '',
  assemblyai: process.env.ASSEMBLYAI_API_KEY || '********************************',
  groq: process.env.GROQ_API_KEY || '',
  openai: process.env.OPENAI_API_KEY || '',
  claude: process.env.CLAUDE_API_KEY || '',
  together: process.env.TOGETHER_API_KEY || ''
};

// 默认配置
const DEFAULT_CONFIG: ServiceConfig = {
  mode: ServiceMode.SEPARATED,
  geminiLive: {
    apiKey: DEFAULT_API_KEYS.gemini,
    model: 'gemini-live-2.5-flash-preview',
    language: 'cmn-CN',
    customPrompt: '',
    profile: 'interview',
    retryAttempts: 3
  },
  separated: {
    transcription: {
      provider: TranscriptionProvider.DEEPGRAM,
      config: {
        apiKey: DEFAULT_API_KEYS.deepgram,
        language: 'zh',
        model: 'nova-2',
        realtime: true,
        punctuation: true,
        timeout: 10000,
        retryAttempts: 2
      }
    },
    ai: {
      provider: AIProvider.GROQ,
      config: {
        apiKey: DEFAULT_API_KEYS.groq,
        model: 'llama-3.1-8b-instant',
        temperature: 0.7,
        maxTokens: 500,
        timeout: 15000,
        retryAttempts: 2
      }
    }
  }
};

/**
 * 服务配置管理器
 * 负责加载、保存和管理服务配置
 */
export class ServiceConfigManager {
  private static instance: ServiceConfigManager | null = null;
  private currentConfig: ServiceConfig;
  private configLoaded = false;
  private readonly configFileName = 'service-config.json';
  private readonly configDir: string;
  private readonly configPath: string;

  constructor() {
    this.configDir = join(app.getPath('userData'), 'geek-assistant-config');
    this.configPath = join(this.configDir, this.configFileName);
    this.currentConfig = { ...DEFAULT_CONFIG };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ServiceConfigManager {
    if (!ServiceConfigManager.instance) {
      ServiceConfigManager.instance = new ServiceConfigManager();
    }
    return ServiceConfigManager.instance;
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    try {
      await this.ensureConfigDirectory();
      await this.loadConfig();
      this.configLoaded = true;
      console.log('Service config manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize service config manager:', error);
      this.currentConfig = { ...DEFAULT_CONFIG };
      this.configLoaded = true;
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ServiceConfig {
    if (!this.configLoaded) {
      console.warn('Config not loaded yet, returning default config');
      return { ...DEFAULT_CONFIG };
    }
    return { ...this.currentConfig };
  }

  /**
   * 更新配置
   */
  async updateConfig(config: ServiceConfig): Promise<void> {
    this.currentConfig = config;
    await this.saveConfig();
    console.log('Service config updated:', config);
  }

  /**
   * 重置为默认配置
   */
  async resetToDefault(): Promise<void> {
    this.currentConfig = { ...DEFAULT_CONFIG };
    await this.saveConfig();
    console.log('Service config reset to default');
  }

  /**
   * 验证配置
   */
  validateConfig(config: ServiceConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证服务模式
    if (!Object.values(ServiceMode).includes(config.mode)) {
      errors.push(`Invalid service mode: ${config.mode}`);
    }

    // 验证Gemini Live配置
    if (config.mode === ServiceMode.GEMINI_LIVE) {
      if (!config.geminiLive) {
        errors.push('Gemini Live config is required when mode is gemini-live');
      } else if (!config.geminiLive.apiKey) {
        errors.push('Gemini API key is required');
      }
    }

    // 验证分离式服务配置
    if (config.mode === ServiceMode.SEPARATED) {
      if (!config.separated) {
        errors.push('Separated service config is required when mode is separated');
      } else {
        // 验证转录服务
        if (!Object.values(TranscriptionProvider).includes(config.separated.transcription.provider)) {
          errors.push(`Invalid transcription provider: ${config.separated.transcription.provider}`);
        }
        if (!config.separated.transcription.config.apiKey) {
          errors.push('Transcription service API key is required');
        }

        // 验证AI服务
        if (!Object.values(AIProvider).includes(config.separated.ai.provider)) {
          errors.push(`Invalid AI provider: ${config.separated.ai.provider}`);
        }
        if (!config.separated.ai.config.apiKey) {
          errors.push('AI service API key is required');
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 创建默认的分离式服务配置
   */
  createSeparatedConfig(transcriptionProvider: TranscriptionProvider, aiProvider: AIProvider): ServiceConfig['separated'] {
    return {
      transcription: {
        provider: transcriptionProvider,
        config: {
          apiKey: this.getDefaultAPIKey(transcriptionProvider),
          language: 'zh',
          realtime: true,
          punctuation: true,
          timeout: 10000,
          retryAttempts: 2
        }
      },
      ai: {
        provider: aiProvider,
        config: {
          apiKey: this.getDefaultAPIKey(aiProvider),
          model: this.getDefaultModel(aiProvider),
          temperature: 0.7,
          maxTokens: 500,
          timeout: 15000,
          retryAttempts: 2
        }
      }
    };
  }

  /**
   * 获取默认API密钥
   */
  private getDefaultAPIKey(provider: TranscriptionProvider | AIProvider): string {
    const keyMap: Record<string, string> = {
      [TranscriptionProvider.DEEPGRAM]: DEFAULT_API_KEYS.deepgram,
      [AIProvider.GROQ]: DEFAULT_API_KEYS.groq,
      [AIProvider.OPENAI]: DEFAULT_API_KEYS.openai,
      [AIProvider.CLAUDE]: DEFAULT_API_KEYS.claude,
      [AIProvider.GEMINI]: DEFAULT_API_KEYS.gemini,
      [AIProvider.TOGETHER]: DEFAULT_API_KEYS.together
    };
    return keyMap[provider] || '';
  }

  /**
   * 获取默认模型
   */
  private getDefaultModel(provider: AIProvider): string {
    const modelMap: Record<AIProvider, string> = {
      [AIProvider.GROQ]: 'llama-3.1-8b-instant',
      [AIProvider.OPENAI]: 'gpt-4o-mini',
      [AIProvider.CLAUDE]: 'claude-3-haiku-20240307',
      [AIProvider.GEMINI]: 'gemini-1.5-flash',
      [AIProvider.TOGETHER]: 'meta-llama/Llama-2-7b-chat-hf',
      [AIProvider.OLLAMA]: 'llama2',
      [AIProvider.CUSTOM]: 'custom-model'
    };
    return modelMap[provider] || 'default-model';
  }

  /**
   * 检查服务是否可用
   */
  isServiceAvailable(provider: TranscriptionProvider | AIProvider): boolean {
    return this.getDefaultAPIKey(provider).length > 0;
  }

  /**
   * 获取可用的服务列表
   */
  getAvailableServices(): { transcription: TranscriptionProvider[]; ai: AIProvider[] } {
    const transcriptionServices = Object.values(TranscriptionProvider).filter(
      (provider) => this.isServiceAvailable(provider)
    );
    
    const aiServices = Object.values(AIProvider).filter(
      (provider) => this.isServiceAvailable(provider)
    );

    return {
      transcription: transcriptionServices,
      ai: aiServices
    };
  }

  /**
   * 确保配置目录存在
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.access(this.configDir);
    } catch {
      await fs.mkdir(this.configDir, { recursive: true });
      console.log('Created config directory:', this.configDir);
    }
  }

  /**
   * 迁移旧的语言代码
   */
  private migrateLanguageCodes(config: ServiceConfig): ServiceConfig {
    let migrated = false;

    // 迁移Gemini Live语言代码
    if (config.geminiLive?.language === 'zh-CN') {
      console.log('🔄 Migrating old Gemini language code zh-CN to cmn-CN');
      config.geminiLive.language = 'cmn-CN';
      migrated = true;
    }

    if (migrated) {
      console.log('✅ Language codes migrated, saving updated config');
      this.saveConfig().catch((error) => {
        console.error('Failed to save migrated config:', error);
      });
    }

    return config;
  }

  /**
   * 加载配置文件
   */
  private async loadConfig(): Promise<void> {
    try {
      const configData = await fs.readFile(this.configPath, 'utf-8');
      let config = JSON.parse(configData);
      
      // 迁移旧配置
      config = this.migrateLanguageCodes(config);
      
      // 验证配置
      const validation = this.validateConfig(config);
      if (validation.valid) {
        this.currentConfig = config;
        console.log('🔍 Service config loaded successfully:', JSON.stringify(config, null, 2));
      } else {
        console.warn('🔍 Invalid config file, using default:', validation.errors);
        console.log('🔍 Default config:', JSON.stringify(DEFAULT_CONFIG, null, 2));
        this.currentConfig = { ...DEFAULT_CONFIG };
      }
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        console.log('Config file not found, creating default config');
        await this.saveConfig();
      } else {
        console.error('Failed to load config:', error);
        throw error;
      }
    }
  }

  /**
   * 保存配置文件
   */
  private async saveConfig(): Promise<void> {
    try {
      console.log('🔧 Saving config to:', this.configPath);
      console.log('🔧 Config data:', JSON.stringify(this.currentConfig, null, 2));
      
      await fs.mkdir(this.configDir, { recursive: true });
      const configData = JSON.stringify(this.currentConfig, null, 2);
      await fs.writeFile(this.configPath, configData, 'utf-8');
      
      console.log('🔧 Service config saved successfully to:', this.configPath);
    } catch (error) {
      console.error('🔧❌ Failed to save config:', error);
      throw error;
    }
  }
}
