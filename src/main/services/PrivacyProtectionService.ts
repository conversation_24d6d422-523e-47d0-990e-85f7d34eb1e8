import { BrowserWindow } from 'electron';

export interface PrivacyConfig {
  enableScreenCaptureProtection?: boolean;
  enableVisibilityProtection?: boolean;
  enableWindowInfoProtection?: boolean;
  enableAPIProtection?: boolean;
  stealthMode?: boolean;
}

/**
 * 隐私保护服务
 * 提供反检测、屏幕保护等功能
 */
export class PrivacyProtectionService {
  private config: PrivacyConfig;

  constructor(config: PrivacyConfig = {}) {
    this.config = {
      enableScreenCaptureProtection: true,
      enableVisibilityProtection: true,
      enableWindowInfoProtection: true,
      enableAPIProtection: true,
      stealthMode: true,
      ...config
    };
  }

  /**
   * 🔒 为窗口应用隐私保护设置
   */
  applyWindowProtection(window: BrowserWindow, isFloating = false): void {
    if (process.platform === 'darwin') {
      this.applyMacOSProtection(window, isFloating);
    } else if (process.platform === 'win32') {
      this.applyWindowsProtection(window, isFloating);
    } else if (process.platform === 'linux') {
      this.applyLinuxProtection(window, isFloating);
    }
  }

  /**
   * 🔒 注入反检测脚本
   */
  injectAntiDetectionScripts(window: BrowserWindow, isFloating = false): void {
    const scripts = this.generateAntiDetectionScripts(isFloating);
    window.webContents.executeJavaScript(scripts).catch((error) => {
      console.log('🔒 Failed to inject anti-detection scripts:', error);
    });
  }

  /**
   * 🔒 macOS 特定保护
   */
  private applyMacOSProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // 屏幕捕获保护
      if (this.config.enableScreenCaptureProtection) {
        (window as any).setContentProtection?.(true);
        (window as any).setPrivacySensitive?.(true);
      }

      // 浮动窗口特殊设置
      if (isFloating && this.config.stealthMode) {
        (window as any).setLevel?.('screen-saver');
        (window as any).setWindowButtonVisibility?.(false);
        (window as any).setVisibleOnAllWorkspaces?.(false);
      }
    } catch (error) {
      console.log('🔒 macOS protection features not available:', error);
    }
  }

  /**
   * 🔒 Windows 特定保护
   */
  private applyWindowsProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // Windows 特定的保护措施
      if (this.config.enableScreenCaptureProtection) {
        // 设置窗口为受保护内容
        window.setContentProtection(true);
      }

      if (isFloating && this.config.stealthMode) {
        // 设置为工具窗口，减少在任务栏的可见性
        window.setSkipTaskbar(true);
      }
    } catch (error) {
      console.log('🔒 Windows protection features not available:', error);
    }
  }

  /**
   * 🔒 Linux 特定保护
   */
  private applyLinuxProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      if (isFloating && this.config.stealthMode) {
        window.setSkipTaskbar(true);
      }
    } catch (error) {
      console.log('🔒 Linux protection features not available:', error);
    }
  }

  /**
   * 🔒 生成反检测脚本
   */
  private generateAntiDetectionScripts(isFloating: boolean): string {
    let scripts = `
      console.log('🔒 Initializing privacy protection...');
    `;

    // 屏幕捕获保护
    if (this.config.enableScreenCaptureProtection) {
      scripts += `
        // 防止屏幕录制检测
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
          navigator.mediaDevices.getDisplayMedia = function() {
            console.log('🔒 Screen capture attempt blocked');
            return Promise.reject(new DOMException('Permission denied', 'NotAllowedError'));
          };
        }
        
        // 防止getUserMedia被滥用
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
          navigator.mediaDevices.getUserMedia = function(constraints) {
            if (constraints && constraints.video && constraints.video.mandatory) {
              console.log('🔒 Suspicious getUserMedia call blocked');
              return Promise.reject(new DOMException('Permission denied', 'NotAllowedError'));
            }
            return originalGetUserMedia.call(this, constraints);
          };
        }
      `;
    }

    // 窗口信息保护
    if (this.config.enableWindowInfoProtection) {
      scripts += `
        // 防止窗口位置检测
        Object.defineProperty(window, 'screenX', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenY', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenLeft', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenTop', {
          get: function() { return 0; },
          configurable: false
        });
        
        // 防止窗口大小检测
        Object.defineProperty(window, 'outerWidth', {
          get: function() { return screen.width; },
          configurable: false
        });
        
        Object.defineProperty(window, 'outerHeight', {
          get: function() { return screen.height; },
          configurable: false
        });
      `;
    }

    // API保护
    if (this.config.enableAPIProtection) {
      scripts += `
        // 防止焦点检测
        const originalFocus = window.focus;
        window.focus = function() {
          if (${isFloating}) {
            // 浮动窗口不获取焦点
            return;
          }
          return originalFocus.call(this);
        };
        
        const originalBlur = window.blur;
        window.blur = function() {
          if (${isFloating}) {
            // 浮动窗口不失去焦点
            return;
          }
          return originalBlur.call(this);
        };
        
        // 防止窗口枚举
        if (window.external && window.external.AddSearchProvider) {
          window.external.AddSearchProvider = function() {
            console.log('🔒 Search provider enumeration blocked');
          };
        }
      `;
    }

    // 浮动窗口特殊保护
    if (this.config.stealthMode && isFloating) {
      scripts += `
        // 浮动窗口特殊保护
        
        // 防止窗口检测
        Object.defineProperty(window, 'name', {
          get: function() { return ''; },
          set: function() { return; },
          configurable: false
        });
        
        // 防止opener检测
        Object.defineProperty(window, 'opener', {
          get: function() { return null; },
          set: function() { return; },
          configurable: false
        });
        
        // 防止parent检测
        Object.defineProperty(window, 'parent', {
          get: function() { return window; },
          configurable: false
        });
        
        Object.defineProperty(window, 'top', {
          get: function() { return window; },
          configurable: false
        });
        
        // 防止iframe检测
        Object.defineProperty(window, 'frameElement', {
          get: function() { return null; },
          configurable: false
        });
        
        // 防止窗口历史检测
        Object.defineProperty(window.history, 'length', {
          get: function() { return 1; },
          configurable: false
        });
      `;
    }

    scripts += `
      console.log('🔒 Privacy protection initialized successfully');
    `;

    return scripts;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<PrivacyConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): PrivacyConfig {
    return { ...this.config };
  }
}
