import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';
import { app } from 'electron';
import * as https from 'https';
import * as http from 'http';
import * as zlib from 'zlib';
import { URL } from 'url';

/**
 * Azure账号信息接口
 */
export interface AzureAccount {
  id: string;
  name: string;
  email: string;
  password: string;
  voiceTime: number; // 总可用时长（分钟）
  usedTime: number; // 已使用时长（分钟）
  azureCredentials: {
    token: string;
    region: string;
    type: 'jwt';
  };
  registrationSuccess: boolean;
  loginSuccess: boolean;
  subscriptionType: string;
  createdAt: string;
  lastUsed?: string;
  isActive?: boolean;
}

/**
 * 账号使用统计接口
 */
export interface AccountUsage {
  accountId: string;
  usedMinutes: number;
  lastUsed: string;
  sessionCount: number;
}

/**
 * Azure账号池管理器
 * 负责管理多个Azure账号的轮换、使用时长跟踪和自动续期
 */
export class AzureAccountManager extends EventEmitter {
  private accounts: AzureAccount[] = [];
  private currentAccountIndex = 0;
  private usageData: Map<string, AccountUsage> = new Map();
  private accountsFilePath: string;
  private usageFilePath: string;
  private isInitialized = false;
  private usageTrackingInterval: NodeJS.Timeout | null = null;
  private currentSessionStartTime: number | null = null;

  // 配置参数
  private readonly SWITCH_THRESHOLD_MINUTES = 5; // 剩余5分钟时切换账号
  private readonly MAX_USAGE_MINUTES = 30; // 每个账号最大使用时长
  private readonly USAGE_SAVE_INTERVAL = 30000; // 30秒保存一次使用数据

  constructor() {
    super();
    
    // 设置文件路径
    const userDataPath = app.getPath('userData');
    const dataDir = path.join(userDataPath, 'azure-data');
    this.accountsFilePath = path.join(dataDir, 'azure-accounts.json');
    this.usageFilePath = path.join(dataDir, 'azure-usage.json');
  }

  /**
   * 初始化账号管理器
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔑 Initializing Azure Account Manager...');
      
      // 确保数据目录存在
      await this.ensureDataDirectory();
      
      // 加载账号数据
      await this.loadAccounts();
      
      // 加载使用统计
      await this.loadUsageData();
      
      // 验证账号状态
      await this.validateAccounts();
      
      // 选择初始账号
      this.selectBestAccount();
      
      // 启动使用跟踪
      this.startUsageTracking();
      
      this.isInitialized = true;
      console.log(`🔑✅ Azure Account Manager initialized with ${this.accounts.length} accounts`);
      
      this.emit('initialized', {
        totalAccounts: this.accounts.length,
        activeAccounts: this.getActiveAccounts().length,
        totalRemainingMinutes: this.getTotalRemainingMinutes()
      });
      
      return true;
    } catch (error) {
      console.error('🔑❌ Failed to initialize Azure Account Manager:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 获取当前活跃账号
   */
  getCurrentAccount(): AzureAccount | null {
    if (!this.isInitialized || this.accounts.length === 0) {
      return null;
    }

    const account = this.accounts[this.currentAccountIndex];
    if (!account || !this.isAccountUsable(account)) {
      // 当前账号不可用，尝试切换
      if (this.switchToNextAccount()) {
        return this.accounts[this.currentAccountIndex];
      }
      // 如果切换失败，返回第一个账号（即使token过期，后续会自动刷新）
      console.log('🔑📝 No usable accounts, returning first account for token refresh');
      return this.accounts[0];
    }

    return account;
  }

  /**
   * 获取所有账号
   */
  getAllAccounts(): AzureAccount[] {
    return this.accounts;
  }

  /**
   * 获取当前账号的剩余时长（分钟）
   */
  getCurrentAccountRemainingMinutes(): number {
    const account = this.getCurrentAccount();
    if (!account) return 0;
    
    const usage = this.usageData.get(account.id);
    const usedMinutes = usage?.usedMinutes || 0;
    return Math.max(0, this.MAX_USAGE_MINUTES - usedMinutes);
  }

  /**
   * 获取所有账号的总剩余时长
   */
  getTotalRemainingMinutes(): number {
    return this.accounts.reduce((total, account) => {
      if (!this.isAccountUsable(account)) return total;
      const usage = this.usageData.get(account.id);
      const usedMinutes = usage?.usedMinutes || 0;
      return total + Math.max(0, this.MAX_USAGE_MINUTES - usedMinutes);
    }, 0);
  }

  /**
   * 获取活跃账号列表
   */
  getActiveAccounts(): AzureAccount[] {
    return this.accounts.filter(account => this.isAccountUsable(account));
  }

  /**
   * 开始使用会话（开始计时）
   */
  startSession(): void {
    if (this.currentSessionStartTime) {
      console.warn('🔑 Session already started');
      return;
    }
    
    this.currentSessionStartTime = Date.now();
    console.log('🔑 Started usage session');
    
    this.emit('session-started', {
      accountId: this.getCurrentAccount()?.id,
      startTime: this.currentSessionStartTime
    });
  }

  /**
   * 结束使用会话（停止计时）
   */
  endSession(): void {
    if (!this.currentSessionStartTime) {
      return;
    }
    
    const sessionDuration = Date.now() - this.currentSessionStartTime;
    const sessionMinutes = sessionDuration / (1000 * 60);
    
    // 更新当前账号的使用时长
    const currentAccount = this.getCurrentAccount();
    if (currentAccount) {
      this.addUsageTime(currentAccount.id, sessionMinutes);
    }
    
    this.currentSessionStartTime = null;
    console.log(`🔑 Ended usage session: ${sessionMinutes.toFixed(2)} minutes`);
    
    this.emit('session-ended', {
      accountId: currentAccount?.id,
      duration: sessionMinutes
    });
  }

  /**
   * 手动切换到下一个可用账号
   */
  async switchAccount(): Promise<boolean> {
    return this.switchToNextAccount();
  }

  /**
   * 刷新指定账号的token
   */
  async refreshAccountToken(accountId: string): Promise<boolean> {
    try {
      console.log(`🔑 Refreshing token for account: ${accountId}`);

      const account = this.accounts.find(acc => acc.id === accountId);
      if (!account) {
        console.error(`🔑❌ Account not found: ${accountId}`);
        return false;
      }

      // 使用集成的token刷新功能
      const newToken = await this.performTokenRefresh(account);
      if (newToken) {
        account.azureCredentials.token = newToken;
        account.isActive = true;

        // 保存更新后的账号数据
        await this.saveAccounts();

        console.log(`🔑✅ Token refreshed successfully for account: ${account.name}`);
        this.emit('token-refreshed', { accountId, success: true });
        return true;
      } else {
        console.error(`🔑❌ Failed to get new token for account: ${account.name}`);
        this.emit('token-refresh-failed', { accountId, error: 'Token refresh failed' });
        return false;
      }
    } catch (error) {
      console.error(`🔑❌ Failed to refresh token for account ${accountId}:`, error);
      this.emit('token-refresh-failed', { accountId, error });
      return false;
    }
  }

  /**
   * 执行token刷新的核心逻辑
   */
  private async performTokenRefresh(account: AzureAccount): Promise<string | null> {
    try {
      console.log(`🔑🔄 Performing token refresh for: ${account.name}`);

      // 检查是否有保存的session
      const sessionData = await this.loadSessionData(account.email);

      if (sessionData && this.isSessionValid(sessionData)) {
        console.log(`🔑📱 Using cached session for: ${account.name}`);
        return await this.getTokenWithSession(sessionData);
      } else {
        console.log(`🔑🔐 Need to login for: ${account.name}`);
        return await this.loginAndGetToken(account);
      }
    } catch (error) {
      console.error(`🔑❌ Token refresh failed for ${account.name}:`, error);
      return null;
    }
  }

  /**
   * 加载session数据
   */
  private async loadSessionData(email: string): Promise<any> {
    try {
      const userDataPath = app.getPath('userData');
      const sessionPath = path.join(userDataPath, 'azure-data', 'sessions.json');

      if (!fs.existsSync(sessionPath)) {
        return null;
      }

      const sessionData = JSON.parse(await fs.readFile(sessionPath, 'utf-8'));
      return sessionData[email] || null;
    } catch (error) {
      console.log(`🔑⚠️ No session data found for: ${email}`);
      return null;
    }
  }

  /**
   * 检查session是否有效
   */
  private isSessionValid(sessionData: any): boolean {
    if (!sessionData || !sessionData.expiresAt) {
      return false;
    }

    const expirationTime = new Date(sessionData.expiresAt);
    const now = new Date();
    const fiveMinutes = 5 * 60 * 1000;

    return (expirationTime.getTime() - now.getTime()) > fiveMinutes;
  }

  /**
   * 使用session获取token
   */
  private async getTokenWithSession(sessionData: any): Promise<string | null> {
    try {
      console.log(`🔑📡 Getting Azure token with session...`);

      const response = await this.makeHttpRequest('https://www.gankinterview.cn/api/user/azure-stt-token', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'Referer': 'https://www.gankinterview.cn/app',
          'Cookie': this.cookiesToString(sessionData.cookies)
        }
      });

      if (response.statusCode === 200) {
        const data = JSON.parse(response.body);
        if (data.token) {
          console.log(`🔑✅ Got new Azure token from session`);
          return data.token;
        }
      }

      console.log(`🔑⚠️ Session token request failed, need to re-login`);
      return null;
    } catch (error) {
      console.error(`🔑❌ Failed to get token with session:`, error);
      return null;
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('🔑 Cleaning up Azure Account Manager...');
    
    // 结束当前会话
    this.endSession();
    
    // 停止使用跟踪
    if (this.usageTrackingInterval) {
      clearInterval(this.usageTrackingInterval);
      this.usageTrackingInterval = null;
    }
    
    // 保存使用数据
    await this.saveUsageData();
    
    // 清理事件监听器
    this.removeAllListeners();
    
    this.isInitialized = false;
    console.log('🔑✅ Azure Account Manager cleaned up');
  }

  // ==================== 私有方法 ====================

  /**
   * 确保数据目录存在
   */
  private async ensureDataDirectory(): Promise<void> {
    const dataDir = path.dirname(this.accountsFilePath);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
      console.log(`🔑 Created data directory: ${dataDir}`);
    }
  }

  /**
   * 加载账号数据
   */
  private async loadAccounts(): Promise<void> {
    try {
      // 首先尝试从项目数据目录加载
      const data = await fs.readFile(this.accountsFilePath, 'utf-8');
      const accountsData = JSON.parse(data);
      this.accounts = accountsData.accounts || [];
      
      console.log(`🔑 Loaded ${this.accounts.length} accounts from data file`);
    } catch (error) {
      // 如果数据文件不存在，尝试从reverse-analysis目录复制
      try {
        const reverseAnalysisPath = path.join(process.cwd(), 'reverse-analysis', 'accounts.json');
        const data = await fs.readFile(reverseAnalysisPath, 'utf-8');
        const accountsData = JSON.parse(data);
        this.accounts = accountsData.accounts || [];
        
        // 初始化使用时长为0
        this.accounts.forEach(account => {
          account.usedTime = 0;
          account.isActive = true;
        });
        
        // 保存到数据目录
        await this.saveAccounts();
        
        console.log(`🔑 Copied and loaded ${this.accounts.length} accounts from reverse-analysis`);
      } catch (copyError) {
        console.error('🔑❌ Failed to load accounts from both locations:', error, copyError);
        this.accounts = [];
      }
    }
  }

  /**
   * 保存账号数据
   */
  private async saveAccounts(): Promise<void> {
    try {
      const accountsData = {
        accounts: this.accounts,
        summary: {
          totalAccounts: this.accounts.length,
          activeAccounts: this.getActiveAccounts().length,
          totalRemainingMinutes: this.getTotalRemainingMinutes(),
          lastUpdated: new Date().toISOString()
        }
      };

      await fs.writeFile(this.accountsFilePath, JSON.stringify(accountsData, null, 2));
      console.log('🔑 Saved accounts data');
    } catch (error) {
      console.error('🔑❌ Failed to save accounts data:', error);
    }
  }

  /**
   * 加载使用统计数据
   */
  private async loadUsageData(): Promise<void> {
    try {
      const data = await fs.readFile(this.usageFilePath, 'utf-8');
      const usageArray: AccountUsage[] = JSON.parse(data);

      this.usageData.clear();
      usageArray.forEach(usage => {
        this.usageData.set(usage.accountId, usage);
      });

      console.log(`🔑 Loaded usage data for ${usageArray.length} accounts`);
    } catch (error) {
      console.log('🔑 No existing usage data found, starting fresh');
      this.usageData.clear();
    }
  }

  /**
   * 保存使用统计数据
   */
  private async saveUsageData(): Promise<void> {
    try {
      const usageArray = Array.from(this.usageData.values());
      await fs.writeFile(this.usageFilePath, JSON.stringify(usageArray, null, 2));
      console.log('🔑 Saved usage data');
    } catch (error) {
      console.error('🔑❌ Failed to save usage data:', error);
    }
  }

  /**
   * 验证账号状态
   */
  private async validateAccounts(): Promise<void> {
    console.log('🔑 Validating account tokens...');

    for (const account of this.accounts) {
      try {
        // 检查token是否过期
        const tokenValid = this.isTokenValid(account.azureCredentials.token);
        account.isActive = tokenValid && account.loginSuccess;

        if (!tokenValid) {
          console.log(`🔑⚠️ Token expired for account: ${account.name}`);
          // 可以在这里触发自动刷新
        }
      } catch (error) {
        console.error(`🔑❌ Failed to validate account ${account.name}:`, error);
        account.isActive = false;
      }
    }

    const activeCount = this.getActiveAccounts().length;
    console.log(`🔑 Account validation complete: ${activeCount}/${this.accounts.length} active`);
  }

  /**
   * 检查JWT token是否有效
   */
  private isTokenValid(token: string): boolean {
    try {
      if (!token || token.length < 10) {
        return false;
      }

      // 检查是否是模拟token（用于测试）
      if (token.includes('mock_signature_for_testing_purposes_only')) {
        console.log('🔑🧪 Using mock token for testing');
        return true; // 允许模拟token通过验证
      }

      // 解析JWT token的payload部分
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.log('🔑⚠️ Invalid JWT format');
        return false;
      }

      try {
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        const exp = payload.exp;

        if (!exp) {
          console.log('🔑⚠️ No expiration time in token');
          return false;
        }

        // 检查是否在过期时间前5分钟
        const expirationTime = exp * 1000;
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;

        const isValid = (expirationTime - now) > fiveMinutes;
        if (!isValid) {
          const expDate = new Date(expirationTime);
          console.log(`🔑⚠️ Token expired at: ${expDate.toISOString()}`);
        }

        return isValid;
      } catch (parseError) {
        // 如果无法解析payload，但token格式看起来合理，允许通过
        if (token.length > 100 && parts.length === 3) {
          console.log('🔑⚠️ Cannot parse token payload, but format looks valid');
          return true;
        }
        return false;
      }
    } catch (error) {
      console.error('🔑❌ Failed to validate JWT token:', error);
      return false;
    }
  }

  /**
   * 检查账号是否可用
   */
  private isAccountUsable(account: AzureAccount): boolean {
    if (!account.isActive || !account.loginSuccess) {
      return false;
    }

    // 检查使用时长
    const usage = this.usageData.get(account.id);
    const usedMinutes = usage?.usedMinutes || 0;
    const remainingMinutes = this.MAX_USAGE_MINUTES - usedMinutes;

    return remainingMinutes > 0;
  }

  /**
   * 选择最佳账号
   */
  private selectBestAccount(): void {
    const activeAccounts = this.getActiveAccounts();
    if (activeAccounts.length === 0) {
      console.warn('🔑⚠️ No active accounts available');
      return;
    }

    // 选择剩余时长最多的账号
    let bestAccount = activeAccounts[0];
    let bestRemainingTime = this.getAccountRemainingMinutes(bestAccount);

    for (let i = 1; i < activeAccounts.length; i++) {
      const account = activeAccounts[i];
      const remainingTime = this.getAccountRemainingMinutes(account);

      if (remainingTime > bestRemainingTime) {
        bestAccount = account;
        bestRemainingTime = remainingTime;
      }
    }

    // 找到最佳账号在原数组中的索引
    this.currentAccountIndex = this.accounts.findIndex(acc => acc.id === bestAccount.id);

    console.log(`🔑 Selected account: ${bestAccount.name} (${bestRemainingTime.toFixed(1)} min remaining)`);

    this.emit('account-selected', {
      account: bestAccount,
      remainingMinutes: bestRemainingTime
    });
  }

  /**
   * 获取指定账号的剩余时长
   */
  private getAccountRemainingMinutes(account: AzureAccount): number {
    const usage = this.usageData.get(account.id);
    const usedMinutes = usage?.usedMinutes || 0;
    return Math.max(0, this.MAX_USAGE_MINUTES - usedMinutes);
  }

  /**
   * 切换到下一个可用账号
   */
  private switchToNextAccount(): boolean {
    const activeAccounts = this.getActiveAccounts();
    if (activeAccounts.length === 0) {
      console.log('🔑📝 No active accounts available for switching, will use first account');
      // 不发出错误事件，只是返回false
      return false;
    }

    // 找到当前账号在活跃账号列表中的位置
    const currentAccount = this.accounts[this.currentAccountIndex];
    const currentActiveIndex = activeAccounts.findIndex(acc => acc.id === currentAccount?.id);

    // 选择下一个活跃账号
    const nextActiveIndex = (currentActiveIndex + 1) % activeAccounts.length;
    const nextAccount = activeAccounts[nextActiveIndex];

    // 更新当前账号索引
    this.currentAccountIndex = this.accounts.findIndex(acc => acc.id === nextAccount.id);

    const remainingMinutes = this.getAccountRemainingMinutes(nextAccount);

    console.log(`🔑 Switched to account: ${nextAccount.name} (${remainingMinutes.toFixed(1)} min remaining)`);

    this.emit('account-switched', {
      previousAccount: currentAccount,
      newAccount: nextAccount,
      remainingMinutes
    });

    return true;
  }

  /**
   * 添加使用时长
   */
  private addUsageTime(accountId: string, minutes: number): void {
    const existing = this.usageData.get(accountId);

    if (existing) {
      existing.usedMinutes += minutes;
      existing.lastUsed = new Date().toISOString();
      existing.sessionCount += 1;
    } else {
      this.usageData.set(accountId, {
        accountId,
        usedMinutes: minutes,
        lastUsed: new Date().toISOString(),
        sessionCount: 1
      });
    }

    // 检查是否需要切换账号
    const remainingMinutes = this.getCurrentAccountRemainingMinutes();
    if (remainingMinutes <= this.SWITCH_THRESHOLD_MINUTES) {
      console.log(`🔑⚠️ Account approaching limit (${remainingMinutes.toFixed(1)} min remaining), switching...`);
      this.switchToNextAccount();
    }

    this.emit('usage-updated', {
      accountId,
      usedMinutes: this.usageData.get(accountId)?.usedMinutes,
      remainingMinutes: this.getCurrentAccountRemainingMinutes()
    });
  }

  /**
   * 启动使用跟踪
   */
  private startUsageTracking(): void {
    if (this.usageTrackingInterval) {
      clearInterval(this.usageTrackingInterval);
    }

    this.usageTrackingInterval = setInterval(async () => {
      // 如果有活跃会话，更新使用时长
      if (this.currentSessionStartTime) {
        const sessionDuration = Date.now() - this.currentSessionStartTime;
        const sessionMinutes = sessionDuration / (1000 * 60);

        const currentAccount = this.getCurrentAccount();
        if (currentAccount) {
          // 临时更新使用时长（不重置会话开始时间）
          const tempUsage = this.usageData.get(currentAccount.id);
          const baseUsage = tempUsage?.usedMinutes || 0;

          // 检查是否需要切换账号
          const totalUsed = baseUsage + sessionMinutes;
          const remaining = this.MAX_USAGE_MINUTES - totalUsed;

          if (remaining <= this.SWITCH_THRESHOLD_MINUTES) {
            console.log(`🔑⚠️ Account ${currentAccount.name} approaching limit, switching...`);
            this.endSession(); // 结束当前会话
            this.switchToNextAccount();
            this.startSession(); // 在新账号上开始会话
          }
        }
      }

      // 定期保存使用数据
      await this.saveUsageData();
    }, this.USAGE_SAVE_INTERVAL);

    console.log('🔑 Started usage tracking');
  }

  /**
   * 登录并获取token
   */
  private async loginAndGetToken(account: AzureAccount): Promise<string | null> {
    try {
      console.log(`🔑🔐 Logging in for account: ${account.name}`);

      // 执行登录
      const loginData = await this.performLogin(account.email, account.password);
      if (!loginData) {
        console.error(`🔑❌ Login failed for: ${account.name}`);
        return null;
      }

      // 获取Azure token
      const token = await this.getTokenWithSession(loginData);
      if (token) {
        // 保存session数据
        await this.saveSessionData(account.email, loginData);
        return token;
      }

      return null;
    } catch (error) {
      console.error(`🔑❌ Login and token retrieval failed for ${account.name}:`, error);
      return null;
    }
  }

  /**
   * 执行登录
   */
  private async performLogin(email: string, password: string): Promise<any> {
    try {
      console.log(`🔑🔐 Performing login for: ${email}`);

      // 第一步：获取登录页面
      const loginPageResponse = await this.makeHttpRequest('https://www.gankinterview.cn/auth/login', {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        }
      });

      if (loginPageResponse.statusCode !== 200) {
        throw new Error(`Failed to load login page: ${loginPageResponse.statusCode}`);
      }

      // 提取cookies
      const cookies = this.extractCookies(loginPageResponse.headers);

      // 第二步：提交登录API请求（JSON格式）
      const loginData = JSON.stringify({
        email: email,
        password: password,
        callbackUrl: '/app'
      });

      const loginResponse = await this.makeHttpRequest('https://www.gankinterview.cn/api/auth/sign-in/email', {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'Referer': 'https://www.gankinterview.cn/auth/login',
          'Origin': 'https://www.gankinterview.cn',
          'Cookie': this.cookiesToString(cookies)
        },
        body: loginData
      });

      // 更新cookies
      const newCookies = this.extractCookies(loginResponse.headers);
      const allCookies = new Map([...cookies, ...newCookies]);

      // 检查登录是否成功
      if (loginResponse.statusCode === 200) {
        const loginResult = JSON.parse(loginResponse.body);
        if (loginResult.token || !loginResult.error) {
          console.log(`🔑✅ Login successful for: ${email}`);

          return {
            cookies: allCookies,
            userToken: loginResult.token,
            expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1小时后过期
            createdAt: new Date().toISOString()
          };
        } else {
          throw new Error(`Login failed: ${loginResult.error}`);
        }
      } else {
        throw new Error(`Login failed with status: ${loginResponse.statusCode}`);
      }
    } catch (error) {
      console.error(`🔑❌ Login failed for ${email}:`, error);
      return null;
    }
  }

  /**
   * HTTP请求封装 - 与原始account-manager.js完全一致
   */
  private async makeHttpRequest(url: string, options: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const requestModule = isHttps ? https : http;

      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'application/json, text/html, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          ...options.headers
        }
      };

      if (options.cookies) {
        requestOptions.headers['Cookie'] = options.cookies;
      }

      if (options.body) {
        requestOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
      }

      const req = requestModule.request(requestOptions, (res) => {
        let stream = res;
        const encoding = res.headers['content-encoding'];

        // 处理压缩编码
        if (encoding === 'gzip') {
          stream = res.pipe(zlib.createGunzip());
        } else if (encoding === 'deflate') {
          stream = res.pipe(zlib.createInflate());
        } else if (encoding === 'br') {
          stream = res.pipe(zlib.createBrotliDecompress());
        }

        let data = '';
        stream.on('data', chunk => data += chunk);
        stream.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data,
            cookies: res.headers['set-cookie'] || []
          });
        });

        stream.on('error', reject);
      });

      req.on('error', reject);

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  }

  /**
   * 处理cookies - 与原始代码一致
   */
  private processCookies(cookieArray: string[], cookieMap: Map<string, string>): void {
    cookieArray.forEach(cookieHeader => {
      const parts = cookieHeader.split(';')[0].split('=');
      if (parts.length === 2) {
        cookieMap.set(parts[0].trim(), parts[1].trim());
      }
    });
  }

  /**
   * 提取cookies
   */
  private extractCookies(headers: any): Map<string, string> {
    const cookies = new Map<string, string>();
    const setCookieHeaders = headers['set-cookie'] || [];
    this.processCookies(setCookieHeaders, cookies);
    return cookies;
  }

  /**
   * 将cookies Map转换为字符串
   */
  private cookiesToString(cookies: Map<string, string>): string {
    return Array.from(cookies.entries())
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');
  }

  /**
   * 保存session数据
   */
  private async saveSessionData(email: string, sessionData: any): Promise<void> {
    try {
      const userDataPath = app.getPath('userData');
      const sessionPath = path.join(userDataPath, 'azure-data', 'sessions.json');

      let sessions: any = {};
      try {
        const existingData = await fs.readFile(sessionPath, 'utf-8');
        sessions = JSON.parse(existingData);
      } catch {
        // 文件不存在，使用空对象
      }

      sessions[email] = sessionData;

      await fs.writeFile(sessionPath, JSON.stringify(sessions, null, 2));
      console.log(`🔑💾 Session saved for: ${email}`);
    } catch (error) {
      console.error(`🔑❌ Failed to save session for ${email}:`, error);
    }
  }
}
