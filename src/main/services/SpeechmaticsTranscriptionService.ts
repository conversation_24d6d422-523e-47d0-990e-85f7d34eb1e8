import { ITranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { EventEmitter } from 'events';
import WebSocket from 'ws';

export interface SpeechmaticsConfig extends TranscriptionConfig {
  apiKey: string;
  language?: string;
  model?: string;
  realtime?: boolean;
  punctuation?: boolean;
  timeout?: number;
  retryAttempts?: number;
  sampleRate?: number;
}

/**
 * Speechmatics 转录服务
 * 提供实时语音转录功能
 */
export class SpeechmaticsTranscriptionService extends EventEmitter implements ITranscriptionService {
  private config: SpeechmaticsConfig;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isTranscribing = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private sessionId: string | null = null;

  constructor(config: SpeechmaticsConfig) {
    super();
    this.config = {
      realtime: true,
      punctuation: true,
      timeout: 10000,
      retryAttempts: 2,
      sampleRate: 16000,
      ...config
    };
    console.log('🎙️ SpeechmaticsTranscriptionService created');
    console.log('🎙️ Config language:', this.config.language);
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎙️ Initializing Speechmatics transcription service...');
      
      if (!this.config.apiKey) {
        throw new Error('Speechmatics API key is required');
      }

      // 测试API连接
      const testResult = await this.testConnection();
      if (!testResult) {
        throw new Error('Failed to connect to Speechmatics API');
      }

      console.log('🎙️✅ Speechmatics transcription service initialized successfully');
      return true;
    } catch (error) {
      console.error('🎙️❌ Failed to initialize Speechmatics transcription service:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    try {
      if (this.isTranscribing) {
        console.log('🎙️ Transcription already active');
        return true;
      }

      console.log('🎙️ Starting Speechmatics transcription...');
      this.updateStatus(TranscriptionStatus.CONNECTING);

      // 创建WebSocket连接
      const success = await this.createWebSocketConnection();
      
      if (success) {
        this.isTranscribing = true;
        this.updateStatus(TranscriptionStatus.LISTENING);
        console.log('🎙️✅ Speechmatics transcription started');
        return true;
      } else {
        throw new Error('Failed to establish WebSocket connection');
      }
    } catch (error) {
      console.error('🎙️❌ Failed to start Speechmatics transcription:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    try {
      console.log('🎙️ Stopping Speechmatics transcription...');
      
      if (this.ws) {
        // 发送结束消息
        this.ws.send(JSON.stringify({
          message: 'EndOfStream'
        }));
        
        setTimeout(() => {
          if (this.ws) {
            this.ws.close();
            this.ws = null;
          }
        }, 1000);
      }

      this.isTranscribing = false;
      this.isConnected = false;
      this.sessionId = null;
      this.clearReconnectTimeout();
      
      this.updateStatus(TranscriptionStatus.DISCONNECTED);
      console.log('🎙️✅ Speechmatics transcription stopped');
    } catch (error) {
      console.error('🎙️❌ Failed to stop Speechmatics transcription:', error);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.ws || !this.isConnected) {
      return;
    }

    try {
      // Speechmatics期望原始PCM音频数据
      this.ws.send(audioData);
    } catch (error) {
      console.error('🎙️❌ Failed to send audio to Speechmatics:', error);
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('https://asr.api.speechmatics.com/v2/jobs', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });

      return response.ok;
    } catch (error) {
      console.error('🎙️❌ Speechmatics connection test failed:', error);
      return false;
    }
  }

  /**
   * 创建WebSocket连接
   */
  private async createWebSocketConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // 生成会话ID
        this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 创建WebSocket连接 - 使用正确的语言代码
        const languageCode = this.getSpeechmaticsLanguageCode(this.config.language || 'en');
        const wsUrl = `wss://eu2.rt.speechmatics.com/v2/${languageCode}`;
        console.log('🎙️ Connecting to Speechmatics with language:', languageCode);

        this.ws = new WebSocket(wsUrl, {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`
          }
        });

        let hasResolved = false;

        this.ws.on('open', () => {
          console.log('🎙️ Speechmatics WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // 发送开始消息
          const startMessage = {
            message: 'StartRecognition',
            audio_format: {
              type: 'raw',
              encoding: 'pcm_s16le',
              sample_rate: this.config.sampleRate || 16000
            },
            transcription_config: {
              language: this.getSpeechmaticsLanguageCode(this.config.language || 'en'),
              enable_partials: true,
              max_delay: 3
            }
          };
          
          this.ws!.send(JSON.stringify(startMessage));
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(true);
          }
        });

        this.ws.on('message', (data) => {
          this.handleWebSocketMessage(data);
        });

        this.ws.on('error', (error) => {
          console.error('🎙️❌ Speechmatics WebSocket error:', error);
          this.handleError(error);
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        this.ws.on('close', (code, reason) => {
          console.log(`🎙️ Speechmatics WebSocket closed: ${code} ${reason}`);
          this.isConnected = false;
          
          if (this.isTranscribing) {
            this.attemptReconnect();
          }
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        // 超时处理
        setTimeout(() => {
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        }, this.config.timeout || 10000);

      } catch (error) {
        console.error('🎙️❌ Failed to create Speechmatics WebSocket:', error);
        resolve(false);
      }
    });
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.message) {
        case 'RecognitionStarted':
          console.log('🎙️ Speechmatics recognition started');
          break;
        case 'AudioAdded':
          // 音频已添加，无需处理
          break;
        case 'AddPartialTranscript':
          this.handleTranscriptMessage(message, false);
          break;
        case 'AddTranscript':
          this.handleTranscriptMessage(message, true);
          break;
        case 'EndOfTranscript':
          console.log('🎙️ Speechmatics transcript ended');
          break;
        case 'Error':
          this.handleError(new Error(message.reason || 'Speechmatics API error'));
          break;
        default:
          console.log('🎙️ Unknown Speechmatics message:', message.message);
      }
    } catch (error) {
      console.error('🎙️❌ Failed to parse Speechmatics message:', error);
    }
  }

  /**
   * 处理转录消息
   */
  private handleTranscriptMessage(message: any, isFinal: boolean): void {
    try {
      // Speechmatics的转录文本在metadata.transcript中
      const transcript = message.metadata?.transcript;
      if (!transcript || !transcript.trim()) return;

      // 计算平均置信度
      let totalConfidence = 0;
      let wordCount = 0;
      if (message.results && Array.isArray(message.results)) {
        message.results.forEach((result: any) => {
          if (result.alternatives && result.alternatives.length > 0) {
            totalConfidence += result.alternatives[0].confidence || 0;
            wordCount++;
          }
        });
      }
      const avgConfidence = wordCount > 0 ? totalConfidence / wordCount : 0.8;

      const result: TranscriptionResult = {
        text: transcript,
        isFinal,
        confidence: avgConfidence,
        timestamp: Date.now(),
        language: this.config.language || 'en'
      };

      console.log(`🎙️${isFinal ? '✅' : '📝'} Speechmatics result: ${result.text}`);
      this.emit('transcription', result);
    } catch (error) {
      console.error('🎙️❌ Failed to process transcript message:', error);
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🎙️❌ Max reconnect attempts reached');
      this.updateStatus(TranscriptionStatus.ERROR);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);
    
    console.log(`🎙️ Attempting to reconnect to Speechmatics (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
    
    this.reconnectTimeout = setTimeout(async () => {
      this.updateStatus(TranscriptionStatus.RECONNECTING);
      const success = await this.createWebSocketConnection();
      
      if (!success) {
        this.attemptReconnect();
      } else {
        this.updateStatus(TranscriptionStatus.LISTENING);
      }
    }, delay);
  }

  /**
   * 清除重连超时
   */
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 更新状态
   */
  private updateStatus(status: TranscriptionStatus): void {
    this.emit('statusChange', status);
  }

  /**
   * 处理错误
   */
  private handleError(error: any): void {
    const errorMessage = typeof error === 'string' ? error : error.message || 'Unknown error';
    console.error('🎙️❌ Speechmatics transcription error:', errorMessage);
    this.emit('error', new Error(errorMessage));
    this.updateStatus(TranscriptionStatus.ERROR);
  }

  /**
   * 获取Speechmatics支持的语言代码
   */
  private getSpeechmaticsLanguageCode(language: string): string {
    const languageMap: Record<string, string> = {
      'zh-CN': 'cmn', // 中文普通话
      'zh': 'cmn',
      'cmn': 'cmn', // 直接支持cmn
      'en-US': 'en',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'es': 'es',
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt',
      'ru': 'ru'
    };

    const mappedLanguage = languageMap[language] || 'en';
    console.log(`🎙️ Language mapping: ${language} -> ${mappedLanguage} (original config: ${this.config.language})`);
    return mappedLanguage;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopTranscription();
    this.removeAllListeners();
  }
}
