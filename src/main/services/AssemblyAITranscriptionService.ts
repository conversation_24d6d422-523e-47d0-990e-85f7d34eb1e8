import { ITranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { EventEmitter } from 'events';
import WebSocket from 'ws';

export interface AssemblyAIConfig extends TranscriptionConfig {
  apiKey: string;
  language?: string;
  model?: string;
  realtime?: boolean;
  punctuation?: boolean;
  timeout?: number;
  retryAttempts?: number;
  sampleRate?: number;
}

/**
 * AssemblyAI 转录服务
 * 提供实时语音转录功能
 */
export class AssemblyAITranscriptionService extends EventEmitter implements ITranscriptionService {
  private config: AssemblyAIConfig;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isTranscribing = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private sessionToken: string | null = null;

  constructor(config: AssemblyAIConfig) {
    super();
    this.config = {
      realtime: true,
      punctuation: true,
      timeout: 10000,
      retryAttempts: 2,
      sampleRate: 16000,
      ...config
    };
    console.log('🔊 AssemblyAITranscriptionService created');
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔊 Initializing AssemblyAI transcription service...');

      if (!this.config.apiKey) {
        throw new Error('AssemblyAI API key is required');
      }

      console.log('🔊✅ AssemblyAI transcription service initialized successfully');
      return true;
    } catch (error) {
      console.error('🔊❌ Failed to initialize AssemblyAI transcription service:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 开始转录
   */
  async startTranscription(): Promise<boolean> {
    try {
      if (this.isTranscribing) {
        console.log('🔊 Transcription already active');
        return true;
      }

      console.log('🔊 Starting AssemblyAI transcription...');
      this.updateStatus(TranscriptionStatus.CONNECTING);

      // 创建WebSocket连接
      const success = await this.createWebSocketConnection();

      if (success) {
        this.isTranscribing = true;
        this.updateStatus(TranscriptionStatus.LISTENING);
        console.log('🔊✅ AssemblyAI transcription started');
        return true;
      } else {
        throw new Error('Failed to establish WebSocket connection');
      }
    } catch (error) {
      console.error('🔊❌ Failed to start AssemblyAI transcription:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    try {
      console.log('🔊 Stopping AssemblyAI transcription...');
      
      if (this.ws) {
        // 发送终止消息
        this.ws.send(JSON.stringify({ terminate_session: true }));
        this.ws.close();
        this.ws = null;
      }

      this.isTranscribing = false;
      this.isConnected = false;
      this.clearReconnectTimeout();
      
      this.updateStatus(TranscriptionStatus.DISCONNECTED);
      console.log('🔊✅ AssemblyAI transcription stopped');
    } catch (error) {
      console.error('🔊❌ Failed to stop AssemblyAI transcription:', error);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.ws || !this.isConnected) {
      return;
    }

    try {
      // AssemblyAI期望原始PCM音频数据
      this.ws.send(audioData);
    } catch (error) {
      console.error('🔊❌ Failed to send audio to AssemblyAI:', error);
    }
  }



  /**
   * 创建WebSocket连接
   */
  private async createWebSocketConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // 使用正确的AssemblyAI WebSocket端点
        const wsUrl = `wss://streaming.assemblyai.com/v3/ws?sample_rate=${this.config.sampleRate || 16000}&format_turns=true&language_detection=true`;
        console.log('🔊 Connecting to AssemblyAI WebSocket:', wsUrl);

        this.ws = new WebSocket(wsUrl, {
          headers: {
            'Authorization': this.config.apiKey  // 直接使用API密钥
          }
        });

        let hasResolved = false;

        this.ws.on('open', () => {
          console.log('🔊 AssemblyAI WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(true);
          }
        });

        this.ws.on('message', (data) => {
          this.handleWebSocketMessage(data);
        });

        this.ws.on('error', (error) => {
          console.error('🔊❌ AssemblyAI WebSocket error:', error);
          this.handleError(error);
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        this.ws.on('close', (code, reason) => {
          console.log(`🔊 AssemblyAI WebSocket closed: ${code} ${reason}`);
          this.isConnected = false;
          
          if (this.isTranscribing) {
            this.attemptReconnect();
          }
          
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        });

        // 超时处理
        setTimeout(() => {
          if (!hasResolved) {
            hasResolved = true;
            resolve(false);
          }
        }, this.config.timeout || 10000);

      } catch (error) {
        console.error('🔊❌ Failed to create AssemblyAI WebSocket:', error);
        resolve(false);
      }
    });
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    try {
      const message = JSON.parse(data.toString());
      console.log('🔊 AssemblyAI received message:', JSON.stringify(message, null, 2));

      if (message.type === 'Begin') {
        const sessionId = message.id;
        const expiresAt = message.expires_at;
        console.log(`🔊✅ AssemblyAI session began: ID=${sessionId}, ExpiresAt=${new Date(expiresAt * 1000).toISOString()}`);
        return;
      }

      if (message.type === 'Turn') {
        const transcript = message.transcript || '';
        const formatted = message.turn_is_formatted;

        console.log(`🔊 AssemblyAI transcript: "${transcript}" (formatted: ${formatted})`);

        if (transcript.trim()) {
          this.handleTranscriptMessage(message, formatted);
        }
        return;
      }

      if (message.type === 'Termination') {
        const audioDuration = message.audio_duration_seconds;
        const sessionDuration = message.session_duration_seconds;
        console.log(`🔊 AssemblyAI session terminated: Audio=${audioDuration}s, Session=${sessionDuration}s`);
        return;
      }

      if (message.error) {
        this.handleError(new Error(message.error));
        return;
      }

    } catch (error) {
      console.error('🔊❌ Failed to parse AssemblyAI message:', error);
      console.error('🔊❌ Raw message data:', data.toString());
    }
  }

  /**
   * 处理转录消息
   */
  private handleTranscriptMessage(message: any, isFinal: boolean): void {
    try {
      const result: TranscriptionResult = {
        text: message.transcript || '',
        isFinal,
        confidence: message.confidence || 0.9, // v3 API不提供confidence
        timestamp: Date.now(),
        language: this.config.language || 'en',
        alternatives: []
      };

      if (result.text && result.text.trim()) {
        console.log(`🔊 AssemblyAI transcription (${isFinal ? 'final' : 'partial'}):`, result.text);
        this.emit('transcription', result);
      }
    } catch (error) {
      console.error('🔊❌ Failed to handle AssemblyAI transcript:', error);
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🔊❌ Max reconnect attempts reached');
      this.updateStatus(TranscriptionStatus.ERROR);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);
    
    console.log(`🔊 Attempting to reconnect to AssemblyAI (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
    
    this.reconnectTimeout = setTimeout(async () => {
      this.updateStatus(TranscriptionStatus.RECONNECTING);

      // 直接尝试重新连接，AssemblyAI v3不需要预先获取token
      const success = await this.createWebSocketConnection();

      if (!success) {
        this.attemptReconnect();
      } else {
        this.updateStatus(TranscriptionStatus.LISTENING);
      }
    }, delay);
  }

  /**
   * 清除重连超时
   */
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 更新状态
   */
  private updateStatus(status: TranscriptionStatus): void {
    this.emit('statusChange', status);
  }

  /**
   * 处理错误
   */
  private handleError(error: any): void {
    const errorMessage = typeof error === 'string' ? error : error.message || 'Unknown error';
    console.error('🔊❌ AssemblyAI transcription error:', errorMessage);
    this.emit('error', new Error(errorMessage));
    this.updateStatus(TranscriptionStatus.ERROR);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopTranscription();
    this.removeAllListeners();
  }
}
