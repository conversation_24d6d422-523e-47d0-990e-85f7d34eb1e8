import { BaseTranscriptionService, TranscriptionConfig, TranscriptionResult, TranscriptionStatus } from './ITranscriptionService';
import { createClient, LiveTranscriptionEvents, LiveClient } from '@deepgram/sdk';

export class DeepgramTranscriptionService extends BaseTranscriptionService {
  private client: any = null;
  private liveConnection: any = null;
  private isConnected = false;
  private isTranscribing = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  constructor(config: TranscriptionConfig) {
    super(config);
    console.log('DeepgramTranscriptionService created with config:', {
      language: config.language,
      model: config.model,
      realtime: config.realtime
    });
  }

  /**
   * 初始化Deepgram客户端
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing Deepgram client...');
      const apiKey = this.config.apiKey || process.env.DEEPGRAM_API_KEY;
      
      if (!apiKey) {
        throw new Error('Deepgram API key is required');
      }

      this.client = createClient(apiKey);
      console.log('Deepgram client created successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize Deepgram client:', error);
      this.emitError(error as Error);
      return false;
    }
  }

  /**
   * 启动实时转录
   */
  async startTranscription(): Promise<boolean> {
    if (!this.client) {
      console.error('Deepgram client not initialized');
      return false;
    }

    try {
      console.log('Starting Deepgram live transcription...');
      
      const options = {
        model: 'nova-2',
        language: 'zh',
        smart_format: true,
        punctuate: true,
        interim_results: true,
        encoding: 'linear16',
        sample_rate: 16000,
        channels: 1
      };

      console.log('🎙️ Deepgram options:', JSON.stringify(options, null, 2));

      this.liveConnection = this.client.listen.live(options);
      this.setupEventHandlers();
      
      this.updateStatus(TranscriptionStatus.CONNECTING);
      
      // 等待连接建立
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.liveConnection.on(LiveTranscriptionEvents.Open, () => {
          clearTimeout(timeout);
          resolve(true);
        });

        this.liveConnection.on(LiveTranscriptionEvents.Error, (error: any) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.isConnected = true;
      this.isTranscribing = true;
      this.reconnectAttempts = 0;
      this.updateStatus(TranscriptionStatus.TRANSCRIBING);
      
      console.log('Deepgram live transcription started successfully');
      return true;
    } catch (error) {
      console.error('Failed to start Deepgram transcription:', error);
      this.emitError(error as Error);
      this.updateStatus(TranscriptionStatus.ERROR);
      return false;
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.liveConnection) return;

    this.liveConnection.on(LiveTranscriptionEvents.Open, () => {
      console.log('Deepgram connection opened');
      this.isConnected = true;
      this.updateStatus(TranscriptionStatus.CONNECTED);
    });

    this.liveConnection.on(LiveTranscriptionEvents.Transcript, (data: any) => {
      console.log('🎙️ Deepgram raw transcript data:', JSON.stringify(data, null, 2));

      if (data.channel?.alternatives?.[0]?.transcript) {
        const transcript = data.channel.alternatives[0].transcript;
        const confidence = data.channel.alternatives[0].confidence;
        const isFinal = data.is_final || false;

        console.log(`🎙️ Deepgram transcript found: "${transcript}" (${isFinal ? 'final' : 'interim'}), confidence: ${confidence}`);

        if (transcript.trim()) {
          const result: TranscriptionResult = {
            text: transcript,
            isFinal,
            confidence,
            timestamp: Date.now()
          };

          console.log(`🎙️✅ Deepgram transcription (${isFinal ? 'final' : 'interim'}):`, transcript);
          this.emitTranscription(result);
        } else {
          console.log('🎙️ Deepgram transcript is empty after trim');
        }
      } else {
        console.log('🎙️ Deepgram transcript data structure invalid or empty');
      }
    });

    this.liveConnection.on(LiveTranscriptionEvents.Error, (error: any) => {
      console.error('Deepgram connection error:', error);
      this.emitError(error);
      this.handleConnectionError();
    });

    this.liveConnection.on(LiveTranscriptionEvents.Close, () => {
      console.log('Deepgram connection closed');
      this.isConnected = false;
      this.updateStatus(TranscriptionStatus.DISCONNECTED);
      
      if (this.isTranscribing) {
        this.handleConnectionError();
      }
    });
  }

  /**
   * 处理连接错误和重连
   */
  private handleConnectionError(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts && this.isTranscribing) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 10000);
      
      console.log(`Attempting to reconnect to Deepgram (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
      
      this.reconnectTimeout = setTimeout(async () => {
        try {
          await this.startTranscription();
        } catch (error) {
          console.error('Reconnection failed:', error);
        }
      }, delay);
    } else {
      console.error('Max reconnection attempts reached or transcription stopped');
      this.updateStatus(TranscriptionStatus.ERROR);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (!this.liveConnection || !this.isConnected) {
      console.log('🎙️ Deepgram not connected, skipping audio data');
      return;
    }

    try {
      console.log(`🎙️ Sending ${audioData.length} bytes to Deepgram`);
      this.liveConnection.send(audioData);
    } catch (error) {
      console.error('Failed to send audio to Deepgram:', error);
      this.emitError(error as Error);
    }
  }

  /**
   * 停止转录
   */
  async stopTranscription(): Promise<void> {
    console.log('Stopping Deepgram transcription...');
    
    this.isTranscribing = false;
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.liveConnection) {
      try {
        this.liveConnection.finish();
      } catch (error) {
        console.error('Error finishing Deepgram connection:', error);
      }
      this.liveConnection = null;
    }

    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.updateStatus(TranscriptionStatus.DISCONNECTED);
    
    console.log('Deepgram transcription stopped');
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopTranscription();
    super.cleanup();
  }
}
