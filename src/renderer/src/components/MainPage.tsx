import React, { useState, useEffect } from 'react'
import { Play, Settings, User, Shield, Upload, Plus, FileText, Users, Calendar, X } from 'lucide-react'
import CompactServiceConfig from './CompactServiceConfig'
import ResumeInputModal from './ResumeInputModal'
import CollaborationMode from './CollaborationMode'
import { getLanguageConfig } from '../utils/i18n'

interface MainPageProps {
  onBackToConfig: () => void
  isConfigured: boolean
}

interface PreparationItem {
  id: string
  title: string
  company: string
  position: string
  updatedAt: string
}

const MainPage: React.FC<MainPageProps> = ({ onBackToConfig, isConfigured }) => {
  const [hasResume, setHasResume] = useState(false)
  const [preparationItems, setPreparationItems] = useState<PreparationItem[]>([])
  const [isStartingInterview, setIsStartingInterview] = useState(false)
  const [isStartingMeeting, setIsStartingMeeting] = useState(false)
  const [showServiceConfig, setShowServiceConfig] = useState(false)
  const [currentLanguage, setCurrentLanguage] = useState('zh')
  const [currentPage, setCurrentPage] = useState<'main' | 'interview' | 'meeting' | 'resume' | 'permissions'>('main')
  const [collaborationMode, setCollaborationMode] = useState<'interview' | 'meeting'>('interview')

  // 获取当前语言配置
  const t = getLanguageConfig(currentLanguage)

  useEffect(() => {
    // 加载简历状态
    const resumeData = localStorage.getItem('geekassistant-resumes')
    setHasResume(!!resumeData)

    // 加载面试准备项
    loadPreparationItems()
  }, [])

  const loadPreparationItems = () => {
    const stored = localStorage.getItem('geekassistant-preparations')
    if (stored) {
      const items = JSON.parse(stored)
      setPreparationItems(items)
    } else {
      // 默认的准备项
      setPreparationItems([
        {
          id: '1',
          title: 'Java后端工程师',
          company: '字节跳动',
          position: '高级Java开发工程师',
          updatedAt: '2025年7月29日'
        },
        {
          id: '2', 
          title: 'HRBP人力资源业务伙伴',
          company: '腾讯',
          position: 'HRBP',
          updatedAt: '2025年7月29日'
        },
        {
          id: '3',
          title: 'React前端工程师', 
          company: '阿里巴巴',
          position: '前端开发工程师',
          updatedAt: '2025年7月29日'
        },
        {
          id: '4',
          title: '产品经理',
          company: '美团',
          position: '高级产品经理',
          updatedAt: '2025年7月29日'
        },
        {
          id: '5',
          title: 'AI/ML算法工程师',
          company: '百度',
          position: '算法工程师',
          updatedAt: '2025年7月29日'
        }
      ])
    }
  }

  const handleStartInterview = async () => {
    setIsStartingInterview(true)
    try {
      setCollaborationMode('interview')
      setCurrentPage('interview')
      console.log('面试模式启动成功')
    } catch (error) {
      console.error('启动面试失败:', error)
    } finally {
      setIsStartingInterview(false)
    }
  }

  const handleStartMeeting = async () => {
    setIsStartingMeeting(true)
    try {
      setCollaborationMode('meeting')
      setCurrentPage('meeting')
      console.log('会议助手启动成功')
    } catch (error) {
      console.error('启动会议助手失败:', error)
    } finally {
      setIsStartingMeeting(false)
    }
  }

  const handleUploadResume = () => {
    // 模拟文件上传
    console.log('上传简历')
    setHasResume(true)
  }

  const handleManualInput = () => {
    setCurrentPage('resume')
  }

  const handleSaveResume = (resumeData: any) => {
    // 保存简历数据到localStorage
    const resumes = JSON.parse(localStorage.getItem('geekassistant-resumes') || '[]')
    const updatedResumes = resumes.find((r: any) => r.id === resumeData.id)
      ? resumes.map((r: any) => r.id === resumeData.id ? resumeData : r)
      : [...resumes, resumeData]

    localStorage.setItem('geekassistant-resumes', JSON.stringify(updatedResumes))
    setHasResume(true)
    console.log('简历已保存:', resumeData)
  }

  const [permissionsData, setPermissionsData] = useState<any>(null)

  const handlePermissions = async () => {
    try {
      const permissions = await window.geekAssistant.checkPermissions()
      console.log('权限状态:', permissions)
      setPermissionsData(permissions)
      setCurrentPage('permissions')
    } catch (error) {
      console.error('检查权限失败:', error)
      setCurrentPage('permissions')
    }
  }

  const handleLanguageSwitch = () => {
    const newLanguage = currentLanguage === 'zh' ? 'en' : 'zh'
    setCurrentLanguage(newLanguage)
    localStorage.setItem('geekassistant-language', newLanguage)
    console.log('语言切换到:', newLanguage === 'zh' ? '中文' : 'English')
  }

  const handleNewPreparation = () => {
    console.log('新建准备项')
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col">
      {/* 现代化顶部导航栏 - Glassmorphism */}
      <div className="bg-white/70 backdrop-blur-xl border-b border-white/20 px-6 py-4 flex-shrink-0 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {/* 面试/会议模式时显示状态信息 */}
            {(currentPage === 'interview' || currentPage === 'meeting') ? (
              <div className="flex items-center space-x-4">
                {/* 状态指示器和标题 */}
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-3 h-3 rounded-full bg-green-500 shadow-lg" />
                    <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75" />
                  </div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                    {currentPage === 'interview' ? t.main.interviewAssistant : t.main.meetingAssistant}
                  </h1>
                </div>

                {/* 连接状态 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm font-medium text-green-700">{t.common.connected}</span>
                </div>

                {/* 当前状态 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-gray-600">
                    {currentPage === 'interview' ? t.collaboration.interviewReady : t.collaboration.meetingReady}
                  </span>
                </div>

                {/* 服务信息 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm font-medium text-gray-700">{t.common.service}:</span>
                  <span className="text-sm font-semibold text-blue-600">Speechmatics + Groq</span>
                </div>
              </div>
            ) : (
              <button
                onClick={handleLanguageSwitch}
                className="px-4 py-2 text-xs font-medium text-slate-600 hover:text-slate-800 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md"
              >
                {currentLanguage === 'zh' ? 'EN' : '中文'}
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* 面试/会议模式时显示关闭按钮 */}
            {(currentPage === 'interview' || currentPage === 'meeting') ? (
              <button
                onClick={() => setCurrentPage('main')}
                className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                title="返回主页"
              >
                <X className="w-4 h-4 text-gray-500 group-hover:text-gray-700" />
              </button>
            ) : (
              <>
                <button
                  onClick={() => setShowServiceConfig(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md"
                >
                  <Settings className="w-4 h-4" />
                  <span>{t.common.settings}</span>
                </button>

                <button
                  onClick={handlePermissions}
                  className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md"
                >
                  <Shield className="w-4 h-4" />
                  <span>{t.common.permissions}</span>
                </button>

                <button className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md">
                  <User className="w-4 h-4" />
                  <span>{t.common.account}</span>
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        {/* 主页面 */}
        {currentPage === 'main' && (
          <div className="h-full flex flex-col px-3 py-2 overflow-y-auto">
            <div className="max-w-5xl mx-auto w-full">
              {/* 主要功能区域 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                {/* 开始面试 - Neumorphism Design */}
                <div className="group relative bg-gradient-to-br from-white to-slate-50 rounded-2xl p-4 shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff] hover:shadow-[12px_12px_24px_#d1d5db,-12px_-12px_24px_#ffffff] transition-all duration-500 border border-white/50">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-purple-50/30 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-[4px_4px_8px_#d1d5db,-4px_-4px_8px_#ffffff] group-hover:scale-110 transition-all duration-500">
                        <Play className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">{t.main.interviewAssistant}</h2>
                        <p className="text-xs text-slate-600 font-medium">{t.main.interviewDescription}</p>
                      </div>
                    </div>

                    <button
                      onClick={handleStartInterview}
                      disabled={isStartingInterview}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium text-sm rounded-xl shadow-[4px_4px_12px_rgba(59,130,246,0.3)] hover:shadow-[6px_6px_20px_rgba(59,130,246,0.4)] transition-all duration-300 disabled:opacity-50 transform hover:scale-[1.02]"
                    >
                      {isStartingInterview ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{currentLanguage === 'zh' ? '启动中...' : 'Starting...'}</span>
                        </>
                      ) : (
                        <>
                          <Play className="w-5 h-5" />
                          <span>{t.main.startInterview}</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* 会议助手 - Neumorphism Design */}
                <div className="group relative bg-gradient-to-br from-white to-slate-50 rounded-2xl p-4 shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff] hover:shadow-[12px_12px_24px_#d1d5db,-12px_-12px_24px_#ffffff] transition-all duration-500 border border-white/50">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/30 to-green-50/30 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-[4px_4px_8px_#d1d5db,-4px_-4px_8px_#ffffff] group-hover:scale-110 transition-all duration-500">
                        <Users className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">{t.main.meetingAssistant}</h2>
                        <p className="text-xs text-slate-600 font-medium">{t.main.meetingDescription}</p>
                      </div>
                    </div>

                    <button
                      onClick={handleStartMeeting}
                      disabled={isStartingMeeting}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium text-sm rounded-xl shadow-[4px_4px_12px_rgba(16,185,129,0.3)] hover:shadow-[6px_6px_20px_rgba(16,185,129,0.4)] transition-all duration-300 disabled:opacity-50 transform hover:scale-[1.02]"
                    >
                      {isStartingMeeting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{currentLanguage === 'zh' ? '启动中...' : 'Starting...'}</span>
                        </>
                      ) : (
                        <>
                          <Users className="w-5 h-5" />
                          <span>{t.main.startMeeting}</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* 简历管理区域 */}
              <div className="relative p-3 mb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center shadow-[4px_4px_8px_rgba(31,38,135,0.2)] ${hasResume ? 'bg-gradient-to-br from-violet-500 to-violet-600' : 'bg-gradient-to-br from-slate-400 to-slate-500'} transition-all duration-300`}>
                      <FileText className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                        {hasResume ? '简历已上传' : '简历管理'}
                      </h3>
                      <p className="text-xs text-slate-600 font-medium">
                        {hasResume ? '个性化面试建议已启用' : '上传简历获得精准建议'}
                      </p>
                    </div>
                  </div>

                  {!hasResume && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={handleUploadResume}
                        className="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white font-medium text-xs rounded-lg shadow-[4px_4px_12px_rgba(139,92,246,0.3)] hover:shadow-[6px_6px_20px_rgba(139,92,246,0.4)] transition-all duration-300 transform hover:scale-105"
                      >
                        <Upload className="w-3 h-3" />
                        <span>上传</span>
                      </button>

                      <button
                        onClick={handleManualInput}
                        className="flex items-center space-x-1 px-3 py-2 bg-white/70 hover:bg-white/90 backdrop-blur-sm border border-white/50 text-slate-700 hover:text-slate-900 font-medium text-xs rounded-lg shadow-[2px_2px_8px_rgba(31,38,135,0.1)] hover:shadow-[4px_4px_12px_rgba(31,38,135,0.15)] transition-all duration-300"
                      >
                        <FileText className="w-3 h-3" />
                        <span>输入</span>
                      </button>
                    </div>
                  )}

                  {hasResume && (
                    <div className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-violet-50 to-violet-100 border border-violet-200 rounded-lg shadow-sm">
                      <div className="w-2 h-2 bg-violet-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-violet-700">已上传</span>
                    </div>
                  )}
                </div>
              </div>

              {/* 面试准备项 */}
              <div className="relative p-3">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-lg flex items-center justify-center shadow-[4px_4px_8px_rgba(31,38,135,0.2)]">
                      <Calendar className="w-4 h-4 text-white" />
                    </div>
                    <h3 className="text-sm font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">面试准备项</h3>
                  </div>
                  <button
                    onClick={handleNewPreparation}
                    className="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-medium text-xs rounded-lg shadow-[4px_4px_12px_rgba(245,158,11,0.3)] hover:shadow-[6px_6px_20px_rgba(245,158,11,0.4)] transition-all duration-300 transform hover:scale-105"
                  >
                    <Plus className="w-3 h-3" />
                    <span>新建</span>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  {preparationItems.map((item) => (
                    <div
                      key={item.id}
                      className="group relative bg-white/80 backdrop-blur-sm hover:bg-white/95 border border-white/50 hover:border-white/70 rounded-xl p-2 cursor-pointer transition-all duration-300 shadow-[2px_2px_8px_rgba(31,38,135,0.1)] hover:shadow-[4px_4px_16px_rgba(31,38,135,0.15)] transform hover:scale-[1.02]"
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-orange-50/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-start justify-between mb-1">
                          <h4 className="font-medium text-slate-800 text-sm group-hover:text-amber-800 transition-colors">{item.title}</h4>
                          <Calendar className="w-3 h-3 text-slate-400 group-hover:text-amber-500 transition-colors" />
                        </div>
                        <p className="text-xs text-slate-600 mb-1 group-hover:text-amber-700 transition-colors">{item.company}</p>
                        <span className="text-xs text-slate-500 group-hover:text-amber-600 transition-colors">
                          {item.updatedAt}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 面试页面 */}
        {(currentPage === 'interview' || currentPage === 'meeting') && (
          <CollaborationMode
            mode={collaborationMode}
            onClose={() => setCurrentPage('main')}
            isEmbedded={true}
            language={currentLanguage}
          />
        )}

        {/* 简历输入页面 */}
        {currentPage === 'resume' && (
          <div className="h-full">
            <ResumeInputModal
              isOpen={true}
              onClose={() => setCurrentPage('main')}
              onSave={handleSaveResume}
              isEmbedded={true}
            />
          </div>
        )}

        {/* 权限管理页面 */}
        {currentPage === 'permissions' && (
          <div className="h-full overflow-y-auto p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <button
                  onClick={() => setCurrentPage('main')}
                  className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors mb-4"
                >
                  <span>←</span>
                  <span>返回主页</span>
                </button>
                <h1 className="text-2xl font-bold text-black">系统权限状态</h1>
                <p className="text-gray-600">检查应用所需的系统权限</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 权限项内容将在这里显示 */}
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <span className="text-lg">🎤</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-black">麦克风权限</h4>
                      <p className="text-sm text-gray-600">用于语音输入和录音</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">状态</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      permissionsData?.microphone?.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {permissionsData?.microphone?.granted ? '已授权' : '未授权'}
                    </span>
                  </div>
                </div>
                {/* 更多权限项... */}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 服务配置模态框 */}
      {showServiceConfig && (
        <CompactServiceConfig onClose={() => setShowServiceConfig(false)} />
      )}
    </div>
  )
}

export default MainPage
