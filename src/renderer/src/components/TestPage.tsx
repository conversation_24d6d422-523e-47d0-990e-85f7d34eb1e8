import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Play, Square, Mic, MicOff, Volume2, Settings, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TestResult {
  type: 'success' | 'warning' | 'error'
  title: string
  message: string
  details?: string
}

const TestPage: React.FC = () => {
  const navigate = useNavigate()
  const [isTestingAudio, setIsTestingAudio] = useState(false)
  const [isTestingServices, setIsTestingServices] = useState(false)
  const [audioTestResult, setAudioTestResult] = useState<TestResult | null>(null)
  const [serviceTestResults, setServiceTestResults] = useState<TestResult[]>([])
  const [currentServiceConfig, setCurrentServiceConfig] = useState<any>(null)

  useEffect(() => {
    loadCurrentConfig()
  }, [])

  const loadCurrentConfig = async () => {
    try {
      const config = await window.geekAssistant.getCurrentServiceConfig()
      setCurrentServiceConfig(config)
    } catch (error) {
      console.error('Failed to load current config:', error)
    }
  }

  const testAudioCapture = async () => {
    setIsTestingAudio(true)
    setAudioTestResult(null)

    try {
      console.log('🧪 TestPage: Starting audio capture test...')
      
      // 检查权限
      const permissions = await window.geekAssistant.checkPermissions()
      console.log('🧪 TestPage: Permissions check result:', permissions)

      if (!permissions.microphone) {
        setAudioTestResult({
          type: 'error',
          title: '麦克风权限未授予',
          message: '请在系统设置中授予麦克风权限',
          details: '前往 系统偏好设置 > 安全性与隐私 > 隐私 > 麦克风'
        })
        return
      }

      // 测试音频捕获
      const audioResult = await window.geekAssistant.testAudioCapture()
      console.log('🧪 TestPage: Audio capture test result:', audioResult)

      if (audioResult.success) {
        setAudioTestResult({
          type: 'success',
          title: '音频捕获测试成功',
          message: `检测到音频设备：${audioResult.deviceName || '默认设备'}`,
          details: `采样率：${audioResult.sampleRate || '16000'}Hz，延迟：${audioResult.latency || '<100'}ms`
        })
      } else {
        setAudioTestResult({
          type: 'error',
          title: '音频捕获测试失败',
          message: audioResult.error || '无法捕获系统音频',
          details: '请检查音频设备设置和权限'
        })
      }
    } catch (error) {
      console.error('🧪 TestPage: Audio test error:', error)
      setAudioTestResult({
        type: 'error',
        title: '音频测试出错',
        message: error.message || '测试过程中发生错误',
        details: '请重试或检查系统设置'
      })
    } finally {
      setIsTestingAudio(false)
    }
  }

  const testServices = async () => {
    setIsTestingServices(true)
    setServiceTestResults([])

    try {
      console.log('🧪 TestPage: Starting services test...')
      
      if (!currentServiceConfig) {
        setServiceTestResults([{
          type: 'error',
          title: '服务配置测试失败',
          message: '无法获取当前服务配置',
          details: '请先配置AI服务'
        }])
        return
      }

      const results: TestResult[] = []

      if (currentServiceConfig.mode === 'gemini-live') {
        // 测试Gemini Live
        console.log('🧪 TestPage: Testing Gemini Live...')
        try {
          const success = await window.geekAssistant.initializeGemini(
            currentServiceConfig.geminiLive.apiKey,
            '',
            'interview',
            'cmn-CN'
          )
          
          if (success) {
            results.push({
              type: 'success',
              title: 'Gemini Live 连接成功',
              message: '已成功连接到Google Gemini Live API',
              details: `模型：${currentServiceConfig.geminiLive.model || 'gemini-live-2.5-flash-preview'}`
            })
          } else {
            results.push({
              type: 'error',
              title: 'Gemini Live 连接失败',
              message: '无法连接到Gemini Live API',
              details: '请检查API密钥和网络连接'
            })
          }
        } catch (error) {
          results.push({
            type: 'error',
            title: 'Gemini Live 测试出错',
            message: error.message || '测试过程中发生错误',
            details: '请检查API密钥和配置'
          })
        }
      } else if (currentServiceConfig.mode === 'separated') {
        // 测试分离式服务
        console.log('🧪 TestPage: Testing separated services...')
        
        // 测试转录服务
        const transcriptionProvider = currentServiceConfig.separated.transcription.provider
        console.log('🧪 TestPage: Testing transcription service:', transcriptionProvider)
        
        try {
          // 这里可以添加具体的转录服务测试逻辑
          results.push({
            type: 'success',
            title: `${transcriptionProvider.toUpperCase()} 转录服务`,
            message: '转录服务配置正常',
            details: `语言：${currentServiceConfig.separated.transcription.config.language || 'zh-CN'}`
          })
        } catch (error) {
          results.push({
            type: 'error',
            title: `${transcriptionProvider.toUpperCase()} 转录服务失败`,
            message: '转录服务连接失败',
            details: '请检查API密钥和网络连接'
          })
        }

        // 测试AI服务
        const aiProvider = currentServiceConfig.separated.ai.provider
        console.log('🧪 TestPage: Testing AI service:', aiProvider)
        
        try {
          // 这里可以添加具体的AI服务测试逻辑
          results.push({
            type: 'success',
            title: `${aiProvider.toUpperCase()} AI服务`,
            message: 'AI服务配置正常',
            details: `模型：${currentServiceConfig.separated.ai.config.model || 'default'}`
          })
        } catch (error) {
          results.push({
            type: 'error',
            title: `${aiProvider.toUpperCase()} AI服务失败`,
            message: 'AI服务连接失败',
            details: '请检查API密钥和网络连接'
          })
        }
      }

      setServiceTestResults(results)
    } catch (error) {
      console.error('🧪 TestPage: Services test error:', error)
      setServiceTestResults([{
        type: 'error',
        title: '服务测试出错',
        message: error.message || '测试过程中发生错误',
        details: '请重试或检查服务配置'
      }])
    } finally {
      setIsTestingServices(false)
    }
  }

  const testFullWorkflow = async () => {
    console.log('🧪 TestPage: Starting full workflow test...')
    
    // 先测试音频
    await testAudioCapture()
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 再测试服务
    await testServices()
  }

  const handleBack = () => {
    navigate('/')
  }

  const getResultIcon = (type: TestResult['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  const getResultBgColor = (type: TestResult['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'error':
        return 'bg-red-50 border-red-200'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">系统测试</h1>
                <p className="text-sm text-gray-600">测试音频捕获和AI服务连接</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容 */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 测试控制面板 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">测试控制</h2>
            
            <div className="space-y-4">
              <button
                onClick={testAudioCapture}
                disabled={isTestingAudio}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isTestingAudio ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>测试音频中...</span>
                  </>
                ) : (
                  <>
                    <Mic className="w-4 h-4" />
                    <span>测试音频捕获</span>
                  </>
                )}
              </button>

              <button
                onClick={testServices}
                disabled={isTestingServices}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isTestingServices ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>测试服务中...</span>
                  </>
                ) : (
                  <>
                    <Settings className="w-4 h-4" />
                    <span>测试AI服务</span>
                  </>
                )}
              </button>

              <button
                onClick={testFullWorkflow}
                disabled={isTestingAudio || isTestingServices}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>完整测试</span>
              </button>
            </div>

            {/* 当前配置信息 */}
            {currentServiceConfig && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">当前配置</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>模式：{currentServiceConfig.mode === 'gemini-live' ? 'Gemini Live' : '分离式服务'}</div>
                  {currentServiceConfig.mode === 'separated' && (
                    <>
                      <div>转录：{currentServiceConfig.separated?.transcription?.provider?.toUpperCase()}</div>
                      <div>AI：{currentServiceConfig.separated?.ai?.provider?.toUpperCase()}</div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 测试结果 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">测试结果</h2>
            
            <div className="space-y-4">
              {/* 音频测试结果 */}
              {audioTestResult && (
                <div className={`p-4 rounded-lg border ${getResultBgColor(audioTestResult.type)}`}>
                  <div className="flex items-start space-x-3">
                    {getResultIcon(audioTestResult.type)}
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{audioTestResult.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{audioTestResult.message}</p>
                      {audioTestResult.details && (
                        <p className="text-xs text-gray-500 mt-2">{audioTestResult.details}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* 服务测试结果 */}
              {serviceTestResults.map((result, index) => (
                <div key={index} className={`p-4 rounded-lg border ${getResultBgColor(result.type)}`}>
                  <div className="flex items-start space-x-3">
                    {getResultIcon(result.type)}
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{result.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                      {result.details && (
                        <p className="text-xs text-gray-500 mt-2">{result.details}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* 空状态 */}
              {!audioTestResult && serviceTestResults.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Volume2 className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">还没有测试结果</p>
                  <p className="text-sm">点击上方按钮开始测试</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 mb-2">测试说明</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>音频捕获测试：</strong>检查系统音频权限和设备状态，确保能够捕获系统音频。</p>
            <p><strong>AI服务测试：</strong>验证当前配置的AI服务连接状态和API密钥有效性。</p>
            <p><strong>完整测试：</strong>依次执行音频和服务测试，全面检查系统状态。</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestPage
