import React, { useState } from 'react'
import { X, Save, User, Briefcase, GraduationCap, FileText } from 'lucide-react'

interface ResumeInputModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (resumeData: any) => void
  initialData?: any
  isEmbedded?: boolean
}

const ResumeInputModal: React.FC<ResumeInputModalProps> = ({ isOpen, onClose, onSave, initialData, isEmbedded = false }) => {
  const [activeTab, setActiveTab] = useState(0)
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    location: initialData?.location || '',
    summary: initialData?.summary || '',
    experience: initialData?.experience || '',
    education: initialData?.education || '',
    skills: initialData?.skills || '',
    projects: initialData?.projects || ''
  })

  const tabs = [
    { id: 0, name: '基本信息', icon: User },
    { id: 1, name: '个人简介', icon: FileText },
    { id: 2, name: '工作经验', icon: Briefcase },
    { id: 3, name: '教育背景', icon: GraduationCap },
    { id: 4, name: '技能特长', icon: '🛠️' },
    { id: 5, name: '项目经验', icon: '🚀' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    const resumeData = {
      id: initialData?.id || Date.now().toString(),
      ...formData,
      updatedAt: new Date().toISOString()
    }
    onSave(resumeData)
    onClose()
  }

  if (!isOpen) return null

  if (isEmbedded) {
    return (
      <div className="h-full flex flex-col bg-white">
        {/* 嵌入模式的头部 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-black">个人简历信息</h1>
                <p className="text-sm text-gray-600">完善您的个人信息，获得更精准的面试建议</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors"
            >
              <span>←</span>
              <span>返回主页</span>
            </button>
          </div>
        </div>

        {/* Tab导航 */}
        <div className="bg-white border-b border-gray-200 px-6">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-3 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {typeof tab.icon === 'string' ? (
                  <span className="text-sm">{tab.icon}</span>
                ) : (
                  <tab.icon className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
          <div className="max-w-2xl mx-auto">
            {/* Tab内容将在这里显示 */}
            {activeTab === 0 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">基本信息</h3>
                  <p className="text-sm text-gray-600">填写您的个人基本信息</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的姓名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">电话</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的电话"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">地址</label>
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的地址"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Tab 1: 个人简介 */}
            {activeTab === 1 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">个人简介</h3>
                  <p className="text-sm text-gray-600">简要介绍您的专业背景和优势</p>
                </div>
                <textarea
                  value={formData.summary}
                  onChange={(e) => handleInputChange('summary', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请简要介绍您的专业背景、工作经验、技能特长和职业目标..."
                />
              </div>
            )}

            {/* Tab 2: 工作经验 */}
            {activeTab === 2 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">工作经验</h3>
                  <p className="text-sm text-gray-600">详细描述您的工作经历</p>
                </div>
                <textarea
                  value={formData.experience}
                  onChange={(e) => handleInputChange('experience', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的工作经验，包括：&#10;• 公司名称和职位&#10;• 工作时间（如：2020年1月 - 2023年12月）&#10;• 主要职责和成就&#10;• 使用的技术和工具"
                />
              </div>
            )}

            {/* Tab 3: 教育背景 */}
            {activeTab === 3 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">教育背景</h3>
                  <p className="text-sm text-gray-600">填写您的教育经历</p>
                </div>
                <textarea
                  value={formData.education}
                  onChange={(e) => handleInputChange('education', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的教育背景，包括：&#10;• 学校名称&#10;• 专业和学历&#10;• 就读时间&#10;• 主要课程或成就"
                />
              </div>
            )}

            {/* Tab 4: 技能特长 */}
            {activeTab === 4 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">技能特长</h3>
                  <p className="text-sm text-gray-600">列出您的专业技能</p>
                </div>
                <textarea
                  value={formData.skills}
                  onChange={(e) => handleInputChange('skills', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请列出您的技能，例如：&#10;• 编程语言：Java, Python, JavaScript&#10;• 框架技术：Spring Boot, React, Vue.js&#10;• 数据库：MySQL, MongoDB, Redis&#10;• 工具软件：Git, Docker, Jenkins"
                />
              </div>
            )}

            {/* Tab 5: 项目经验 */}
            {activeTab === 5 && (
              <div className="bg-white rounded-lg p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">项目经验</h3>
                  <p className="text-sm text-gray-600">描述您参与的重要项目</p>
                </div>
                <textarea
                  value={formData.projects}
                  onChange={(e) => handleInputChange('projects', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的项目经验，包括：&#10;• 项目名称和描述&#10;• 项目时间和规模&#10;• 您的角色和职责&#10;• 使用的技术栈&#10;• 项目成果和收获"
                />
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 px-6 py-4 bg-white">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              <span>💡 提示：完善的简历信息有助于获得更精准的面试建议</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-black transition-colors"
              >
                取消
              </button>

              <button
                onClick={handleSave}
                className="btn-hover flex items-center space-x-2 px-4 py-2 bg-black text-white font-medium rounded-lg"
              >
                <Save className="w-4 h-4" />
                <span>保存简历</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-white bg-opacity-90 flex items-center justify-center p-8">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-xl">
        {/* 头部 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-black">个人简历信息</h1>
                <p className="text-sm text-gray-600">完善您的个人信息，获得更精准的面试建议</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <X className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Tab导航 */}
        <div className="bg-white border-b border-gray-200 px-6">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-3 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {typeof tab.icon === 'string' ? (
                  <span className="text-sm">{tab.icon}</span>
                ) : (
                  <tab.icon className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="h-[calc(90vh-180px)] overflow-y-auto p-6 bg-gray-50">
          <div className="max-w-2xl mx-auto">
            {/* Tab 0: 基本信息 */}
            {activeTab === 0 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">基本信息</h3>
                  <p className="text-sm text-gray-600">填写您的个人基本信息</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的姓名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">电话</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的电话"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">地址</label>
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的地址"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Tab 1: 个人简介 */}
            {activeTab === 1 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">个人简介</h3>
                  <p className="text-sm text-gray-600">简要介绍您的专业背景和优势</p>
                </div>
                <textarea
                  value={formData.summary}
                  onChange={(e) => handleInputChange('summary', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请简要介绍您的专业背景、工作经验、技能特长和职业目标..."
                />
              </div>
            )}

            {/* Tab 2: 工作经验 */}
            {activeTab === 2 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">工作经验</h3>
                  <p className="text-sm text-gray-600">详细描述您的工作经历</p>
                </div>
                <textarea
                  value={formData.experience}
                  onChange={(e) => handleInputChange('experience', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的工作经验，包括：&#10;• 公司名称和职位&#10;• 工作时间（如：2020年1月 - 2023年12月）&#10;• 主要职责和成就&#10;• 使用的技术和工具"
                />
              </div>
            )}

            {/* Tab 3: 教育背景 */}
            {activeTab === 3 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">教育背景</h3>
                  <p className="text-sm text-gray-600">填写您的教育经历</p>
                </div>
                <textarea
                  value={formData.education}
                  onChange={(e) => handleInputChange('education', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的教育背景，包括：&#10;• 学校名称&#10;• 专业和学历&#10;• 就读时间&#10;• 主要课程或成就"
                />
              </div>
            )}

            {/* Tab 4: 技能特长 */}
            {activeTab === 4 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">技能特长</h3>
                  <p className="text-sm text-gray-600">列出您的专业技能</p>
                </div>
                <textarea
                  value={formData.skills}
                  onChange={(e) => handleInputChange('skills', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请列出您的技能，例如：&#10;• 编程语言：Java, Python, JavaScript&#10;• 框架技术：Spring Boot, React, Vue.js&#10;• 数据库：MySQL, MongoDB, Redis&#10;• 工具软件：Git, Docker, Jenkins"
                />
              </div>
            )}

            {/* Tab 5: 项目经验 */}
            {activeTab === 5 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">项目经验</h3>
                  <p className="text-sm text-gray-600">描述您参与的重要项目</p>
                </div>
                <textarea
                  value={formData.projects}
                  onChange={(e) => handleInputChange('projects', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的项目经验，包括：&#10;• 项目名称和描述&#10;• 项目时间和规模&#10;• 您的角色和职责&#10;• 使用的技术栈&#10;• 项目成果和收获"
                />
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 px-6 py-4 bg-white">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              <span>💡 提示：完善的简历信息有助于获得更精准的面试建议</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-black transition-colors"
              >
                取消
              </button>

              <button
                onClick={handleSave}
                className="btn-hover flex items-center space-x-2 px-4 py-2 bg-black text-white font-medium rounded-lg"
              >
                <Save className="w-4 h-4" />
                <span>保存简历</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResumeInputModal
