import React, { useState, useEffect, useRef } from 'react'
import { <PERSON>, Mic, MicOff, Volume2, Settings, RotateCcw, Trash2, Send, Eye, EyeOff } from 'lucide-react'

const FloatingWindow: React.FC = () => {
  const [transcription, setTranscription] = useState('')
  const [aiResponse, setAiResponse] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [opacity, setOpacity] = useState(0.9)
  const [manualInput, setManualInput] = useState('')
  const [status, setStatus] = useState('准备中...')
  const [isGeminiConnected, setIsGeminiConnected] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [conversation, setConversation] = useState<Array<{
    type: 'user' | 'ai'
    content: string
    timestamp: Date
  }>>([])

  const transcriptionRef = useRef<HTMLDivElement>(null)
  const conversationRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const initializeSystem = async () => {
      try {
        console.log('🪟 FloatingWindow: Initializing system...')
        
        setStatus('正在连接服务...')

        // 直接启动会话（这里会根据当前配置连接API）
        const sessionStarted = await window.geekAssistant.startSession()
        if (!sessionStarted) {
          setStatus('错误：无法连接到AI服务，请检查配置和网络')
          return
        }

        setStatus('正在启动音频捕获...')
        
        // 启动音频捕获
        const audioStarted = await window.geekAssistant.startAudioCapture()
        if (audioStarted) {
          setStatus('准备就绪')
          setIsConnected(true)
          setIsGeminiConnected(true)
        } else {
          setStatus('错误：无法启动音频捕获。请检查系统音频权限')
        }
      } catch (error) {
        console.error('🪟 FloatingWindow: 初始化错误:', error)
        setStatus(`错误：${error.message || error}`)
      }
    }

    // 设置事件监听器
    const setupEventListeners = () => {
      const statusUnsubscribe = window.geekAssistant.onStatusUpdate((status: string) => {
        console.log('🪟 FloatingWindow: Status update:', status)
        setStatus(status)
      })

      const transcriptionUnsubscribe = window.geekAssistant.onTranscriptionUpdate((text: string) => {
        console.log('🪟 FloatingWindow: Transcription update:', text)
        const cleanText = cleanTranscription(text)
        if (cleanText.trim()) {
          setTranscription(prev => {
            const newText = prev + ' ' + cleanText
            const words = newText.split(' ')
            return words.length > 100 ? words.slice(-80).join(' ') : newText
          })

          // 更新对话历史
          setConversation(prev => {
            const lastMessage = prev[prev.length - 1]
            if (lastMessage && lastMessage.type === 'user') {
              return [
                ...prev.slice(0, -1),
                {
                  ...lastMessage,
                  content: lastMessage.content + ' ' + cleanText,
                  timestamp: new Date()
                }
              ]
            } else {
              return [
                ...prev,
                {
                  type: 'user',
                  content: cleanText,
                  timestamp: new Date()
                }
              ]
            }
          })
        }
      })

      const aiResponseUnsubscribe = window.geekAssistant.onAIResponse((response: any) => {
        console.log('🪟 FloatingWindow: AI response:', response)
        const responseText = response.text || response
        if (responseText.trim()) {
          setAiResponse(responseText)
          setConversation(prev => [
            ...prev,
            {
              type: 'ai',
              content: responseText,
              timestamp: new Date()
            }
          ])
        }
      })

      const errorUnsubscribe = window.geekAssistant.onSessionError((error: string) => {
        console.error('🪟 FloatingWindow: Session error:', error)
        setStatus(`错误：${error}`)
        setIsConnected(false)
        setIsGeminiConnected(false)
      })

      const closedUnsubscribe = window.geekAssistant.onSessionClosed(() => {
        console.log('🪟 FloatingWindow: Session closed')
        setStatus('会话已关闭')
        setIsConnected(false)
        setIsGeminiConnected(false)
      })

      return () => {
        statusUnsubscribe()
        transcriptionUnsubscribe()
        aiResponseUnsubscribe()
        errorUnsubscribe()
        closedUnsubscribe()
      }
    }

    initializeSystem()
    const cleanup = setupEventListeners()

    return () => {
      cleanup()
      window.geekAssistant.stopAudioCapture()
      window.geekAssistant.stopSession()
    }
  }, [])

  // 自动滚动
  useEffect(() => {
    if (conversationRef.current) {
      conversationRef.current.scrollTop = conversationRef.current.scrollHeight
    }
  }, [aiResponse, conversation])

  useEffect(() => {
    if (transcriptionRef.current) {
      transcriptionRef.current.scrollTop = transcriptionRef.current.scrollHeight
    }
  }, [transcription])

  const handleClose = async () => {
    await window.geekAssistant.stopAudioCapture()
    await window.geekAssistant.stopSession()
    await window.geekAssistant.closeFloatingWindow()
  }

  const handleOpacityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOpacity(parseFloat(e.target.value))
  }

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (manualInput.trim()) {
      // 添加到对话历史
      setConversation(prev => [
        ...prev,
        {
          type: 'user',
          content: manualInput.trim(),
          timestamp: new Date()
        }
      ])
      setManualInput('')
    }
  }

  const handleReconnect = async () => {
    setStatus('正在重连...')
    try {
      // 重新初始化服务
      const success = await window.geekAssistant.initializeServices()
      if (success) {
        await window.geekAssistant.startSession()
        await window.geekAssistant.startAudioCapture()
        setIsGeminiConnected(true)
        setIsConnected(true)
        setStatus('重连成功')
      } else {
        setStatus('重连失败')
      }
    } catch (error) {
      setStatus(`重连错误：${error}`)
    }
  }

  const handleClearHistory = () => {
    setConversation([])
    setTranscription('')
    setAiResponse('')
  }

  const cleanTranscription = (text: string): string => {
    return text.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '')
  }

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  return (
    <div 
      className="rounded-lg overflow-hidden shadow-lg border border-gray-200 flex flex-col"
      style={{
        opacity,
        backgroundColor: `rgba(255, 255, 255, ${opacity})`,
        height: isMinimized ? '60px' : '100vh',
        width: isMinimized ? '300px' : '100vw',
        transition: 'all 0.3s ease'
      }}
    >
      {/* 标题栏 */}
      <div className="bg-black text-white p-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-xs">{status}</span>
        </div>
        <div className="flex items-center space-x-1">
          {isGeminiConnected ? (
            <Mic className="w-4 h-4 text-green-400" />
          ) : (
            <MicOff className="w-4 h-4 text-red-400" />
          )}
          {!isGeminiConnected && (
            <button
              onClick={handleReconnect}
              className="p-1 hover:bg-gray-800 rounded"
              title="重连"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          )}
          <button 
            onClick={toggleMinimize}
            className="p-1 hover:bg-gray-800 rounded"
            title={isMinimized ? "展开" : "最小化"}
          >
            {isMinimized ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
          <button className="p-1 hover:bg-gray-800 rounded">
            <Settings className="w-4 h-4" />
          </button>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-800 rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 主内容区域 - 只在非最小化时显示 */}
      {!isMinimized && (
        <>
          <div className="flex-1 overflow-hidden flex flex-col">
            {/* 实时转录区域 */}
            <div className="bg-gray-50 p-3 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-xs font-medium text-gray-500">实时转录</h3>
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              </div>
              <div 
                ref={transcriptionRef}
                className="text-sm text-gray-700 max-h-20 overflow-y-auto"
              >
                {transcription || (isConnected ? '正在聆听...' : '等待音频输入')}
              </div>
            </div>

            {/* 对话历史区域 */}
            <div ref={conversationRef} className="flex-1 p-3 overflow-y-auto bg-white">
              {conversation.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-400">
                    {isConnected ? (
                      <div>
                        <div className="loading-dots mb-2">AI 正在聆听</div>
                        <p className="text-xs">开始说话，AI 将为您提供实时回复</p>
                      </div>
                    ) : (
                      <div>
                        <p className="mb-2">等待连接...</p>
                        <p className="text-xs">请稍候，正在初始化 AI 助手</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {conversation.map((message, index) => (
                    <div key={index} className={`${message.type === 'ai' ? 'ml-0' : 'mr-0'}`}>
                      <div className={`text-xs text-gray-500 mb-1 ${message.type === 'ai' ? 'text-left' : 'text-right'}`}>
                        {message.type === 'ai' ? 'AI 助手' : '您'} • {message.timestamp.toLocaleTimeString()}
                      </div>
                      <div className={`p-2 rounded-lg text-sm ${
                        message.type === 'ai' 
                          ? 'bg-gray-100 text-black' 
                          : 'bg-blue-50 text-blue-900 ml-8'
                      }`}>
                        {message.type === 'ai' ? (
                          <div dangerouslySetInnerHTML={{ __html: message.content }} />
                        ) : (
                          <p>{message.content}</p>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {/* 显示正在回复的AI响应 */}
                  {aiResponse && conversation[conversation.length - 1]?.type !== 'ai' && (
                    <div className="ml-0">
                      <div className="text-xs text-gray-500 mb-1 text-left">
                        AI 助手 • 正在回复...
                      </div>
                      <div className="p-2 rounded-lg text-sm bg-gray-100 text-black">
                        <div dangerouslySetInnerHTML={{ __html: aiResponse }} />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 手动输入区域 */}
            <form onSubmit={handleManualSubmit} className="border-t border-gray-200 p-2 flex items-center">
              <input
                type="text"
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value)}
                placeholder="输入问题..."
                className="flex-1 px-3 py-2 border border-gray-200 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black"
              />
              <button
                type="submit"
                className="px-3 py-2 bg-black text-white rounded-r-lg hover:bg-gray-800"
              >
                <Send className="w-4 h-4" />
              </button>
            </form>
          </div>

          {/* 底部控制栏 */}
          <div className="bg-gray-50 p-2 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-1">
                  <Volume2 className="w-4 h-4 text-gray-600" />
                  <span className="text-xs text-gray-600">系统音频</span>
                </div>
                {conversation.length > 0 && (
                  <button
                    onClick={handleClearHistory}
                    className="flex items-center space-x-1 px-2 py-1 text-xs text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="清除对话历史"
                  >
                    <Trash2 className="w-3 h-3" />
                    <span>清除</span>
                  </button>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-600">透明度</span>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={opacity}
                  onChange={handleOpacityChange}
                  className="w-20"
                />
                <span className="text-xs text-gray-500 w-8">
                  {Math.round(opacity * 100)}%
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default FloatingWindow
