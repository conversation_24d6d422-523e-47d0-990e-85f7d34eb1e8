import React from 'react'
import { <PERSON><PERSON>ron<PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON>, <PERSON>, Shield, Zap, Star, CheckCircle } from 'lucide-react'

interface WelcomePageProps {
  onComplete: () => void
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onComplete }) => {
  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col">
      {/* 主要内容区域 */}
      <div className="flex-1 flex items-center justify-center px-8">
        <div className="max-w-5xl w-full text-center">
          {/* 头部Logo和标题 - Modern Design */}
          <div className="mb-12">
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-500 via-purple-500 to-violet-600 rounded-3xl flex items-center justify-center shadow-[8px_8px_24px_rgba(59,130,246,0.3),-8px_-8px_24px_rg<PERSON>(255,255,255,0.8)] relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
              <Sparkles className="w-12 h-12 text-white relative z-10" />
            </div>
            <h1 className="text-lg font-bold bg-gradient-to-r from-slate-800 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">Geek 助手</h1>
            <p className="text-sm text-slate-600 font-medium">您的专业 AI 面试伙伴</p>
          </div>

          {/* 核心功能网格 - Modern Glassmorphism */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="group relative bg-white/70 backdrop-blur-xl rounded-2xl p-6 shadow-[0_8px_32px_rgba(31,38,135,0.15)] border border-white/30 hover:shadow-[0_12px_40px_rgba(31,38,135,0.2)] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-blue-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-[4px_4px_12px_rgba(59,130,246,0.3)] group-hover:scale-110 transition-all duration-500">
                  <Mic className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-medium text-slate-800 text-sm mb-2 group-hover:text-blue-800 transition-colors">实时转录</h3>
                <p className="text-xs text-slate-600 group-hover:text-blue-700 transition-colors">高精度语音识别</p>
              </div>
            </div>

            <div className="group relative bg-white/70 backdrop-blur-xl rounded-2xl p-6 shadow-[0_8px_32px_rgba(31,38,135,0.15)] border border-white/30 hover:shadow-[0_12px_40px_rgba(31,38,135,0.2)] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-purple-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-[4px_4px_12px_rgba(147,51,234,0.3)] group-hover:scale-110 transition-all duration-500">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-medium text-slate-800 text-sm mb-2 group-hover:text-purple-800 transition-colors">AI 智能回答</h3>
                <p className="text-xs text-slate-600 group-hover:text-purple-700 transition-colors">智能建议生成</p>
              </div>
            </div>

            <div className="group relative bg-white/70 backdrop-blur-xl rounded-2xl p-6 shadow-[0_8px_32px_rgba(31,38,135,0.15)] border border-white/30 hover:shadow-[0_12px_40px_rgba(31,38,135,0.2)] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-emerald-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-[4px_4px_12px_rgba(16,185,129,0.3)] group-hover:scale-110 transition-all duration-500">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-medium text-slate-800 text-sm mb-2 group-hover:text-emerald-800 transition-colors">隐私保护</h3>
                <p className="text-xs text-slate-600 group-hover:text-emerald-700 transition-colors">本地安全处理</p>
              </div>
            </div>

            <div className="group relative bg-white/70 backdrop-blur-xl rounded-2xl p-6 shadow-[0_8px_32px_rgba(31,38,135,0.15)] border border-white/30 hover:shadow-[0_12px_40px_rgba(31,38,135,0.2)] transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-amber-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-[4px_4px_12px_rgba(245,158,11,0.3)] group-hover:scale-110 transition-all duration-500">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-medium text-slate-800 text-sm mb-2 group-hover:text-amber-800 transition-colors">极速响应</h3>
                <p className="text-xs text-slate-600 group-hover:text-amber-700 transition-colors">毫秒级反应</p>
              </div>
            </div>
          </div>

          {/* 开始按钮 - Modern Design */}
          <div className="text-center">
            <button
              onClick={onComplete}
              className="group relative inline-flex items-center space-x-3 px-12 py-4 bg-gradient-to-r from-blue-500 via-purple-500 to-violet-600 hover:from-blue-600 hover:via-purple-600 hover:to-violet-700 text-white font-medium text-sm rounded-2xl shadow-[8px_8px_24px_rgba(59,130,246,0.3)] hover:shadow-[12px_12px_32px_rgba(59,130,246,0.4)] transition-all duration-500 transform hover:scale-105"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">开始使用 Geek 助手</span>
              <ChevronRight className="w-6 h-6 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </div>

      {/* 底部版本信息 - Glassmorphism */}
      <div className="bg-white/50 backdrop-blur-xl border-t border-white/30 px-8 py-6">
        <div className="flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full shadow-[0_0_8px_rgba(16,185,129,0.4)] animate-pulse"></div>
            <span className="font-semibold bg-gradient-to-r from-slate-700 to-slate-500 bg-clip-text text-transparent">Geek Assistant v1.0.9</span>
          </div>
          <span className="text-slate-400">•</span>
          <span className="font-medium text-slate-600">准备好开始了吗？</span>
        </div>
      </div>
    </div>
  )
}

export default WelcomePage
