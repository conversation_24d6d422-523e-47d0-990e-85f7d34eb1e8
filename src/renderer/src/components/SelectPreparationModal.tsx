import React, { useState } from 'react'
import { X, Calendar, MapPin, Building, User, Target, FileText, Play, Edit, Trash2 } from 'lucide-react'

interface Preparation {
  id: string
  title: string
  company: string
  position: string
  location: string
  interviewDate: string
  interviewType: string
  description: string
  requirements: string[]
  keyPoints: string[]
  questions: string[]
  createdAt: Date
  updatedAt: Date
}

interface SelectPreparationModalProps {
  isOpen: boolean
  onClose: () => void
  preparations: Preparation[]
  onSelect?: (preparation: Preparation) => void
  onEdit?: (preparation: Preparation) => void
  onDelete?: (id: string) => void
}

const SelectPreparationModal: React.FC<SelectPreparationModalProps> = ({
  isOpen,
  onClose,
  preparations,
  onSelect,
  onEdit,
  onDelete
}) => {
  const [selectedPreparation, setSelectedPreparation] = useState<Preparation | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  if (!isOpen) return null

  const filteredPreparations = preparations.filter(prep =>
    prep.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prep.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    prep.position.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (preparation: Preparation) => {
    setSelectedPreparation(preparation)
  }

  const handleStartInterview = async () => {
    if (selectedPreparation) {
      // 保存选中的准备项到localStorage
      localStorage.setItem('geekassistant-selected-preparation', JSON.stringify(selectedPreparation))
      
      if (onSelect) {
        onSelect(selectedPreparation)
      }
      
      // 启动协作模式
      try {
        const success = await window.geekAssistant.enterCollaborationMode()
        if (success) {
          console.log('面试模式启动成功，使用准备项:', selectedPreparation.title)
          onClose()
        }
      } catch (error) {
        console.error('启动面试模式失败:', error)
      }
    }
  }

  const handleEdit = (preparation: Preparation) => {
    if (onEdit) {
      onEdit(preparation)
    }
    onClose()
  }

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这个面试准备项吗？此操作无法撤销。')) {
      if (onDelete) {
        onDelete(id)
      }
      if (selectedPreparation?.id === id) {
        setSelectedPreparation(null)
      }
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '未设置'
    try {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">选择面试准备项</h2>
              <p className="text-blue-100 mt-1">选择一个准备项开始AI面试助手</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          {/* 搜索框 */}
          <div className="mt-4">
            <input
              type="text"
              placeholder="搜索公司、职位或标题..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50"
            />
          </div>
        </div>

        <div className="flex h-[calc(90vh-200px)]">
          {/* 左侧列表 */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            {filteredPreparations.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <FileText className="w-16 h-16 mb-4 text-gray-300" />
                <p className="text-lg font-medium">没有找到面试准备项</p>
                <p className="text-sm">请先创建一个面试准备项</p>
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {filteredPreparations.map((preparation) => (
                  <div
                    key={preparation.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedPreparation?.id === preparation.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => handleSelect(preparation)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{preparation.title}</h3>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div className="flex items-center space-x-2">
                            <Building className="w-4 h-4" />
                            <span>{preparation.company}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4" />
                            <span>{preparation.position}</span>
                          </div>
                          {preparation.interviewDate && (
                            <div className="flex items-center space-x-2">
                              <Calendar className="w-4 h-4" />
                              <span>{formatDate(preparation.interviewDate)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEdit(preparation)
                          }}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="编辑"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDelete(preparation.id)
                          }}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="删除"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-2 flex items-center space-x-2">
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                        {preparation.interviewType}
                      </span>
                      {preparation.location && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          <MapPin className="w-3 h-3" />
                          <span>{preparation.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 右侧详情 */}
          <div className="w-1/2 overflow-y-auto">
            {selectedPreparation ? (
              <div className="p-6">
                <div className="mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {selectedPreparation.title}
                  </h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Building className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-700">{selectedPreparation.company}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-700">{selectedPreparation.position}</span>
                    </div>
                    {selectedPreparation.location && (
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-700">{selectedPreparation.location}</span>
                      </div>
                    )}
                    {selectedPreparation.interviewDate && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-700">{formatDate(selectedPreparation.interviewDate)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {selectedPreparation.description && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <FileText className="w-4 h-4" />
                      <span>职位描述</span>
                    </h4>
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {selectedPreparation.description}
                    </p>
                  </div>
                )}

                {selectedPreparation.requirements && selectedPreparation.requirements.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <Target className="w-4 h-4" />
                      <span>技能要求</span>
                    </h4>
                    <div className="space-y-1">
                      {selectedPreparation.requirements.map((req, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                          <span className="text-sm text-gray-700">{req}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedPreparation.keyPoints && selectedPreparation.keyPoints.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <Target className="w-4 h-4" />
                      <span>重点准备</span>
                    </h4>
                    <div className="space-y-1">
                      {selectedPreparation.keyPoints.map((point, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                          <span className="text-sm text-gray-700">{point}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedPreparation.questions && selectedPreparation.questions.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <FileText className="w-4 h-4" />
                      <span>常见问题</span>
                    </h4>
                    <div className="space-y-2">
                      {selectedPreparation.questions.map((question, index) => (
                        <div key={index} className="p-2 bg-gray-50 rounded text-sm text-gray-700">
                          {question}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <Target className="w-16 h-16 mb-4 text-gray-300" />
                <p className="text-lg font-medium">选择一个准备项</p>
                <p className="text-sm">查看详细信息并开始面试</p>
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {selectedPreparation ? (
                <span>已选择：{selectedPreparation.title}</span>
              ) : (
                <span>请选择一个面试准备项</span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleStartInterview}
                disabled={!selectedPreparation}
                className={`flex items-center space-x-2 px-6 py-2 rounded-lg transition-colors ${
                  selectedPreparation
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <Play className="w-4 h-4" />
                <span>开始面试</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SelectPreparationModal
