import React, { useState } from 'react'
import { ArrowLeft, ArrowRight, Check, <PERSON><PERSON><PERSON>, Zap, Brain, Mic } from 'lucide-react'

interface AIConfigPageProps {
  onComplete: () => void
  onBack: () => void
}

const AIConfigPage: React.FC<AIConfigPageProps> = ({ onComplete, onBack }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedMode, setSelectedMode] = useState<'gemini-live' | 'separated'>('gemini-live')
  const [transcriptionService, setTranscriptionService] = useState('speechmatics')
  const [aiService, setAiService] = useState('groq')
  const [isConfiguring, setIsConfiguring] = useState(false)

  const steps = [
    { id: 1, title: '选择模式', description: '选择您偏好的AI服务模式' },
    { id: 2, title: '配置服务', description: '配置转录和AI服务' },
    { id: 3, title: '完成设置', description: '测试连接并完成配置' }
  ]

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    } else {
      onBack()
    }
  }

  const handleComplete = async () => {
    setIsConfiguring(true)

    try {
      // 构建配置
      let config: any

      if (selectedMode === 'gemini-live') {
        config = {
          mode: 'gemini-live',
          geminiLive: {
            apiKey: 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw',
            model: 'gemini-live-2.5-flash-preview',
            language: 'cmn-CN',
            customPrompt: '',
            profile: 'interview'
          }
        }
      } else {
        config = {
          mode: 'separated',
          separated: {
            transcription: {
              provider: transcriptionService,
              config: {
                apiKey: getDefaultAPIKey(transcriptionService),
                language: 'zh-CN',
                realtime: true
              }
            },
            ai: {
              provider: aiService,
              config: {
                apiKey: getDefaultAPIKey(aiService),
                model: getDefaultModel(aiService),
                temperature: 0.7,
                maxTokens: 500
              }
            }
          }
        }
      }

      console.log('🔧 AIConfigPage: Saving config:', config)

      // 保存配置
      const success = await window.geekAssistant.updateServiceConfig(config)

      if (success) {
        console.log('🔧 AIConfigPage: Config saved successfully')

        // 验证配置是否正确保存
        const savedConfig = await window.geekAssistant.getCurrentServiceConfig()
        console.log('🔧 AIConfigPage: Verified saved config:', savedConfig)

        setTimeout(() => {
          onComplete()
        }, 1000)
      } else {
        console.error('🔧❌ AIConfigPage: Failed to save config')
        alert('配置保存失败，请重试')
      }
    } catch (error) {
      console.error('🔧❌ AIConfigPage: Configuration failed:', error)
      alert('配置过程中发生错误：' + error.message)
    } finally {
      setIsConfiguring(false)
    }
  }

  const getDefaultAPIKey = (provider: string) => {
    const keys: Record<string, string> = {
      'speechmatics': 'hEMM081yWFGCKLSvSWs1Tox6FGk4PLn1',
      'gladia': 'da495f4d-893b-4aac-beb2-c3c313c143fe',
      'deepgram': '****************************************',
      'groq': '********************************************************'
    }
    return keys[provider] || ''
  }

  const getDefaultModel = (provider: string) => {
    const models: Record<string, string> = {
      'groq': 'llama-3.1-8b-instant',
      'together': 'meta-llama/Llama-2-7b-chat-hf',
      'openai': 'gpt-4o-mini'
    }
    return models[provider] || 'default'
  }

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* 头部 */}
      <div className="border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-xl font-bold text-black">AI 服务配置</h1>
              <p className="text-sm text-gray-600">配置您的专属AI助手</p>
            </div>
          </div>

          {/* 步骤指示器 */}
          <div className="flex items-center space-x-3">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-all ${
                  currentStep >= step.id
                    ? 'bg-black text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-6 h-0.5 mx-2 transition-all ${
                    currentStep > step.id ? 'bg-black' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 当前步骤信息 */}
        <div className="text-center mt-4">
          <h2 className="text-lg font-semibold text-black">{steps[currentStep - 1].title}</h2>
          <p className="text-sm text-gray-600">{steps[currentStep - 1].description}</p>
        </div>
      </div>

      {/* 主要配置区域 */}
      <div className="flex-1 overflow-y-auto px-6 py-6">
        <div className="max-w-5xl mx-auto">
          {/* 步骤1: 选择模式 */}
          {currentStep === 1 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Gemini Live */}
              <div
                className={`card-hover p-6 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedMode === 'gemini-live'
                    ? 'border-black bg-black text-white'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                onClick={() => setSelectedMode('gemini-live')}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    selectedMode === 'gemini-live' ? 'bg-white/20' : 'bg-black'
                  }`}>
                    <Zap className={`w-5 h-5 ${selectedMode === 'gemini-live' ? 'text-white' : 'text-white'}`} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                      selectedMode === 'gemini-live'
                        ? 'bg-yellow-400 text-yellow-900'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      推荐
                    </span>
                    {selectedMode === 'gemini-live' && <Check className="w-4 h-4 text-green-300" />}
                  </div>
                </div>

                <h3 className="text-lg font-bold mb-2">Google Gemini Live</h3>
                <p className={`text-sm mb-4 leading-relaxed ${
                  selectedMode === 'gemini-live' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  一体化实时语音AI，直接与Google最新的Gemini模型对话，无需额外配置
                </p>

                <div className="grid grid-cols-3 gap-3 text-xs">
                  <div className="text-center">
                    <div className={selectedMode === 'gemini-live' ? 'text-gray-400' : 'text-gray-500'}>延迟</div>
                    <div className="font-medium text-green-400">{'< 500ms'}</div>
                  </div>
                  <div className="text-center">
                    <div className={selectedMode === 'gemini-live' ? 'text-gray-400' : 'text-gray-500'}>准确率</div>
                    <div className="font-medium text-blue-400">95%+</div>
                  </div>
                  <div className="text-center">
                    <div className={selectedMode === 'gemini-live' ? 'text-gray-400' : 'text-gray-500'}>配置</div>
                    <div className="font-medium text-purple-400">极简</div>
                  </div>
                </div>
              </div>

              {/* 分离式服务 */}
              <div
                className={`card-hover p-6 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedMode === 'separated'
                    ? 'border-black bg-black text-white'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                onClick={() => setSelectedMode('separated')}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    selectedMode === 'separated' ? 'bg-white/20' : 'bg-gray-600'
                  }`}>
                    <Settings className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                      selectedMode === 'separated'
                        ? 'bg-blue-400 text-blue-900'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      灵活
                    </span>
                    {selectedMode === 'separated' && <Check className="w-4 h-4 text-green-300" />}
                  </div>
                </div>

                <h3 className="text-lg font-bold mb-2">分离式服务</h3>
                <p className={`text-sm mb-4 leading-relaxed ${
                  selectedMode === 'separated' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  自由搭配转录和AI服务，支持多种服务商，可根据需求灵活配置
                </p>

                <div className="grid grid-cols-3 gap-3 text-xs">
                  <div className="text-center">
                    <div className={selectedMode === 'separated' ? 'text-gray-400' : 'text-gray-500'}>转录</div>
                    <div className="font-medium text-green-400">多选择</div>
                  </div>
                  <div className="text-center">
                    <div className={selectedMode === 'separated' ? 'text-gray-400' : 'text-gray-500'}>AI</div>
                    <div className="font-medium text-blue-400">多选择</div>
                  </div>
                  <div className="text-center">
                    <div className={selectedMode === 'separated' ? 'text-gray-400' : 'text-gray-500'}>自定义</div>
                    <div className="font-medium text-purple-400">高</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 步骤2: 配置服务 */}
          {currentStep === 2 && selectedMode === 'separated' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 转录服务选择 */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-6 h-6 rounded bg-blue-600 flex items-center justify-center">
                    <Mic className="w-3 h-3 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-black">转录服务</h3>
                </div>

                <div className="space-y-3">
                  {[
                    { id: 'azure', name: 'Azure Speech', icon: '🔷', quota: '6.5小时免费', recommended: true },
                    { id: 'speechmatics', name: 'Speechmatics', icon: '🎙️', quota: '8小时/月', recommended: true },
                    { id: 'gladia', name: 'Gladia', icon: '🎵', quota: '10小时/月', recommended: true },
                    { id: 'deepgram', name: 'Deepgram', icon: '🌊', quota: '$200免费' },
                    { id: 'assemblyai', name: 'AssemblyAI', icon: '🎯', quota: '5小时/月' }
                  ].map((service) => (
                    <div
                      key={service.id}
                      className={`card-hover p-4 rounded-lg border cursor-pointer transition-all ${
                        transcriptionService === service.id
                          ? 'border-black bg-black text-white'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setTranscriptionService(service.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{service.icon}</span>
                          <div>
                            <h4 className="font-semibold text-sm">{service.name}</h4>
                            <p className={`text-xs ${
                              transcriptionService === service.id ? 'text-gray-300' : 'text-gray-600'
                            }`}>
                              {service.quota}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {service.recommended && (
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              transcriptionService === service.id
                                ? 'bg-yellow-400 text-yellow-900'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              推荐
                            </span>
                          )}
                          {transcriptionService === service.id && (
                            <Check className="w-4 h-4 text-green-300" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI服务选择 */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-6 h-6 rounded bg-purple-600 flex items-center justify-center">
                    <Brain className="w-3 h-3 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-black">AI服务</h3>
                </div>

                <div className="space-y-3">
                  {[
                    { id: 'groq', name: 'Groq', icon: '⚡', quota: '14.4K请求/天', recommended: true },
                    { id: 'together', name: 'Together AI', icon: '🤝', quota: '$25免费额度' },
                    { id: 'openai', name: 'OpenAI GPT', icon: '🧠', quota: '需付费使用' }
                  ].map((service) => (
                    <div
                      key={service.id}
                      className={`card-hover p-4 rounded-lg border cursor-pointer transition-all ${
                        aiService === service.id
                          ? 'border-black bg-black text-white'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setAiService(service.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{service.icon}</span>
                          <div>
                            <h4 className="font-semibold text-sm">{service.name}</h4>
                            <p className={`text-xs ${
                              aiService === service.id ? 'text-gray-300' : 'text-gray-600'
                            }`}>
                              {service.quota}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {service.recommended && (
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              aiService === service.id
                                ? 'bg-yellow-400 text-yellow-900'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              推荐
                            </span>
                          )}
                          {aiService === service.id && (
                            <Check className="w-4 h-4 text-green-300" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 步骤2: Gemini Live 配置 */}
          {currentStep === 2 && selectedMode === 'gemini-live' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Check className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-black mb-3">Gemini Live 已就绪</h3>
              <p className="text-gray-600 text-sm max-w-md mx-auto">
                Google Gemini Live 无需额外配置，已为您预设最佳参数，点击下一步即可开始使用
              </p>
            </div>
          )}

          {/* 步骤3: 完成设置 */}
          {currentStep === 3 && (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                {isConfiguring ? (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                ) : (
                  <Check className="w-8 h-8 text-white" />
                )}
              </div>
              <h3 className="text-xl font-bold text-black mb-3">
                {isConfiguring ? '正在配置服务' : '配置完成'}
              </h3>
              <p className="text-gray-600 text-sm max-w-md mx-auto">
                {isConfiguring ? '正在测试连接并保存配置，请稍候...' : '您的AI助手已配置完成，点击完成开始使用'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="border-t border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-black transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>上一步</span>
          </button>

          <div className="text-center">
            <p className="text-sm text-gray-500">
              {selectedMode === 'gemini-live' ? 'Google Gemini Live' : `${transcriptionService} + ${aiService}`}
            </p>
          </div>

          <button
            onClick={handleNext}
            disabled={isConfiguring}
            className="btn-hover flex items-center space-x-2 px-6 py-2 bg-black text-white font-semibold rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>{currentStep === 3 ? '完成配置' : '下一步'}</span>
            {!isConfiguring && <ArrowRight className="w-4 h-4" />}
            {isConfiguring && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
          </button>
        </div>
      </div>
    </div>
  )
}

export default AIConfigPage
