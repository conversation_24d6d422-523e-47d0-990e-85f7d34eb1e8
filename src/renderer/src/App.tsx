import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import WelcomePage from './components/WelcomePage'
import AIConfigPage from './components/AIConfigPage'
import MainPage from './components/MainPage'
import FloatingWindow from './components/FloatingWindow'
import CollaborationMode from './components/CollaborationMode'

// 声明全局类型
declare global {
  interface Window {
    geekAssistant: {
      // 窗口管理
      createFloatingWindow: () => Promise<boolean>
      closeFloatingWindow: () => Promise<boolean>
      enterCollaborationMode: () => Promise<boolean>
      exitCollaborationMode: () => Promise<boolean>
      
      // 服务管理
      initializeServices: () => Promise<boolean>
      switchService: (config: any) => Promise<boolean>
      startSession: () => Promise<boolean>
      stopSession: () => Promise<boolean>
      
      // 音频管理
      startAudioCapture: () => Promise<boolean>
      stopAudioCapture: () => Promise<boolean>
      
      // 配置管理
      getCurrentServiceConfig: () => Promise<any>
      updateServiceConfig: (config: any) => Promise<boolean>
      
      // 传统Gemini API（向后兼容）
      initializeGemini: (apiKey: string, customPrompt?: string, profile?: string, language?: string) => Promise<boolean>
      reconnectGemini: () => Promise<boolean>
      manualReconnect: () => Promise<boolean>
      disconnectGemini: () => Promise<boolean>
      
      // 权限管理
      checkPermissions: () => Promise<any>
      checkScreenRecordingPermission: () => Promise<any>
      checkMicrophonePermission: () => Promise<any>
      checkApiKeyStatus: () => Promise<any>
      checkAudioDeviceStatus: () => Promise<any>
      openSystemPreferences: (pane: string) => Promise<boolean>
      testAudioCapture: () => Promise<any>
      requestMicrophonePermission: () => Promise<any>
      
      // 事件监听
      onStatusUpdate: (callback: (status: string) => void) => () => void
      onTranscriptionUpdate: (callback: (text: string) => void) => () => void
      onAIResponse: (callback: (response: any) => void) => () => void
      onSessionError: (callback: (error: string) => void) => () => void
      onSessionClosed: (callback: () => void) => () => void
    }
  }
}

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'welcome' | 'config' | 'main'>('welcome')
  const [isConfigured, setIsConfigured] = useState(false)

  useEffect(() => {
    // 临时清除localStorage以便测试欢迎页
    localStorage.removeItem('geekassistant-has-seen-welcome')
    localStorage.removeItem('geekassistant-configured')

    // 检查是否已经配置过
    const hasSeenWelcome = localStorage.getItem('geekassistant-has-seen-welcome')
    const hasConfigured = localStorage.getItem('geekassistant-configured')

    if (hasSeenWelcome && hasConfigured) {
      setCurrentStep('main')
      setIsConfigured(true)
    } else if (hasSeenWelcome) {
      setCurrentStep('config')
    }
  }, [])

  const handleWelcomeComplete = () => {
    localStorage.setItem('geekassistant-has-seen-welcome', 'true')
    localStorage.setItem('geekassistant-configured', 'true')
    setIsConfigured(true)
    setCurrentStep('main')
  }

  const handleConfigComplete = () => {
    localStorage.setItem('geekassistant-configured', 'true')
    setIsConfigured(true)
    setCurrentStep('main')
  }

  const handleBackToConfig = () => {
    setCurrentStep('config')
  }

  return (
    <Router>
      <Routes>
        <Route 
          path="/" 
          element={
            currentStep === 'welcome' ? (
              <WelcomePage onComplete={handleWelcomeComplete} />
            ) : currentStep === 'config' ? (
              <AIConfigPage 
                onComplete={handleConfigComplete}
                onBack={() => setCurrentStep('welcome')}
              />
            ) : (
              <MainPage 
                onBackToConfig={handleBackToConfig}
                isConfigured={isConfigured}
              />
            )
          } 
        />
        <Route path="/collaboration" element={<CollaborationMode />} />
        <Route path="/floating" element={<FloatingWindow />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  )
}

export default App
