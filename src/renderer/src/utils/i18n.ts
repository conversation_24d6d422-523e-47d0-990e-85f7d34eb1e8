// 国际化配置
export interface LanguageConfig {
  // 通用
  common: {
    settings: string
    permissions: string
    account: string
    close: string
    back: string
    connected: string
    disconnected: string
    service: string
  }
  
  // 主页
  main: {
    interviewAssistant: string
    interviewDescription: string
    startInterview: string
    meetingAssistant: string
    meetingDescription: string
    startMeeting: string
    resumeManagement: string
    uploadResume: string
    manualInput: string
    interviewPreparation: string
    newPreparation: string
  }
  
  // 面试/会议页面
  collaboration: {
    aiSuggestions: string
    interviewSuggestionDesc: string
    meetingSuggestionDesc: string
    realTimeTranscription: string
    listening: string
    currentTranscription: string
    transcriptionHistory: string
    waitingForAudio: string
    listeningAudio: string
    assistantReady: string
    interviewReady: string
    meetingReady: string
  }
}

export const languages: Record<string, LanguageConfig> = {
  zh: {
    common: {
      settings: '设置',
      permissions: '权限',
      account: '账户',
      close: '关闭',
      back: '返回',
      connected: '已连接',
      disconnected: '未连接',
      service: '服务'
    },
    main: {
      interviewAssistant: 'AI 面试助手',
      interviewDescription: '智能面试模拟与实时建议',
      startInterview: '开始面试',
      meetingAssistant: 'AI 会议助手',
      meetingDescription: '智能会议记录与回复建议',
      startMeeting: '开始会议',
      resumeManagement: '简历管理',
      uploadResume: '上传简历',
      manualInput: '手动输入',
      interviewPreparation: '面试准备',
      newPreparation: '新建准备项'
    },
    collaboration: {
      aiSuggestions: 'AI 智能建议',
      interviewSuggestionDesc: '基于面试问题的回答建议',
      meetingSuggestionDesc: '基于会议内容的回复建议',
      realTimeTranscription: '实时转录',
      listening: '正在监听',
      currentTranscription: '当前转录',
      transcriptionHistory: '转录历史',
      waitingForAudio: '等待音频输入',
      listeningAudio: '正在聆听音频...',
      assistantReady: 'AI 助手准备就绪',
      interviewReady: '面试助手准备就绪',
      meetingReady: '会议助手准备就绪'
    }
  },
  en: {
    common: {
      settings: 'Settings',
      permissions: 'Permissions',
      account: 'Account',
      close: 'Close',
      back: 'Back',
      connected: 'Connected',
      disconnected: 'Disconnected',
      service: 'Service'
    },
    main: {
      interviewAssistant: 'AI Interview Assistant',
      interviewDescription: 'Smart interview simulation with real-time suggestions',
      startInterview: 'Start Interview',
      meetingAssistant: 'AI Meeting Assistant',
      meetingDescription: 'Smart meeting recording with reply suggestions',
      startMeeting: 'Start Meeting',
      resumeManagement: 'Resume Management',
      uploadResume: 'Upload Resume',
      manualInput: 'Manual Input',
      interviewPreparation: 'Interview Preparation',
      newPreparation: 'New Preparation'
    },
    collaboration: {
      aiSuggestions: 'AI Smart Suggestions',
      interviewSuggestionDesc: 'Answer suggestions based on interview questions',
      meetingSuggestionDesc: 'Reply suggestions based on meeting content',
      realTimeTranscription: 'Real-time Transcription',
      listening: 'Listening',
      currentTranscription: 'Current Transcription',
      transcriptionHistory: 'Transcription History',
      waitingForAudio: 'Waiting for audio input',
      listeningAudio: 'Listening to audio...',
      assistantReady: 'AI Assistant Ready',
      interviewReady: 'Interview Assistant Ready',
      meetingReady: 'Meeting Assistant Ready'
    }
  }
}

export const getLanguageConfig = (language: string): LanguageConfig => {
  return languages[language] || languages.zh
}
