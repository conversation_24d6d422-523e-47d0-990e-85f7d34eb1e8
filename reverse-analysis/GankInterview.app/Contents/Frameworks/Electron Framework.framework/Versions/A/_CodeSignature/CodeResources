<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		DaH5sSi9qFOU/Rpm/qrNNY6b/ik=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		8vZwWbxqzsKMQ1nd2hqwWHK4HT8=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			yzNATZvE4SfwLv6UjXHur3T8Vgw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uALdxBKIyaSNHkkMTpA9ewImuOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			EAnAp/UOLlDQk12nnC2/IS3GzJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			u0ZxEx2uP35o7g7fzFuuy3W0j4Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+WBDVYT7bOtOkE/2fdYTENoFx4Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			T2QhDSpFN9dPmhqdGGHSX2JIJMY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		L/1qVVhUDKhooWFlA9vtE4wm/4c=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		/qa4cmHfUAtWflLqNlxGIR2MpiA=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8unZYLtzXu7R1ExRmgyW4l2sDiM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			831V5TpShKZIhn+J3FQjoqZoVEA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Y2NKAJcwTUWt2Jma23xKq/6YuNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			F4+RmaQX7x+lLo4cjLI2zkzIquM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			lCYUZ26yXbYssZbeGa7UrheKzzY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			noJ34y+RRFKqxVrjy1z4yj52DZ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			6y0SdclJ6AMfvDXy9DFF73qXfvc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AZPXtwmZUT4CjtojIG/Bp6nW19Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bGqH7tV+qacb5KV5GMWBznCdYfY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			FbVLZWmklrABt5b44/4/bUAYMpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uhRVCwjnh2JGdxD6Veanox138CA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			erhE6QtCtXCJr2c7LWlLIkR4d6c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			XlAZzvYqt6DPmKbAjrEo8Om8Y1Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			4eJIREz06v/qN8iqh9pTe77SPDw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xlf8zLeyRaxbPfZ3TLuNknflNF4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			PDdQiMyH0zArYT8CgcagFSIgZG0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zdUKK4l1I+2IEoKR4haQV/C6Tv8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			pKC1hbaRxsZ4ftvOH1JJ1oWAfGc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		6ya8x9JL5CvYz73tU71i1gWYm78=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/Dh5J03Lfz3HQIm+xzcFyNJBos4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Pf6rq4zNFGkyBI2G+nYWlDvl65A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/IQemHl21W2R6PysPfU35V54FVc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			CACNtl3+w4sdW7q3wiovHyHv1wA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			J6tZj8OncYpVo9y2Q4sV/sq+GwM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			WZTQE6RF51tNeTsbCsbSCdqb9YA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Is2bW9TSnrzyNU5KlZxYwh7Znq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			yWFWta0X6E/4GH6hWRcE9zqML+M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1DqwZ20qNPGsD+2czzMb+MSN+rI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			sdfI/uESGdVvq15lqYeYeQ0VPks=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bnwggvHV/Q5KgVR8Ta3jy16QPdo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			cz+5ASwM0HXmh4I5QcImY5tJo4c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ANjDUmm58Zqc8cHz8EzgfMKCfy8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			vARuFZ8w0uNfYBCRAfr3w2h3Qxk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5iaxfqcc7tU4Fo0p7VtnTm9ezhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		Fluy4Pam31zB5NRQGw96uXebeSw=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			HqyAs0LV65Fb26a0vJXYhf6Oh5A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			49yaxNnFcykwzHRpBLDhPCWoD/w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			0CabGRX3pio+euA2gWPtR52oszM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			vqfodHaR42n9LD0mfJdGrizXTSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			hPSLNhtiPG1KdbShVq7Qixof35s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uAJh2VPM0MWw7ikWE054Z251aUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bqqAoOhGq2Ge38ib3t7187T2TU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			y8FgyLIi3CD8tkHHddd8+xKMrGg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			XcQOiYeULyj4W+teQsacfEKb+PA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			gSl2L0lwRTEHBRAvC+HeKYnRJkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VoZnIepy8beFgy/yc1SOXD9pw6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			q8aOdA7abj2GSfzvis997v6Pr1Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			pEcho/yw0KeSQGfz+T0nv9yFnxY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<data>
		EYOtGSj8Qs9TyIy8/VxV9n1tdsY=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			md5HllCPtuA6F8pgRMAA/6NAdbA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			JEqs2Q8uljApSxwJBWi8uoZ/1cQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ENYRt+eLzI0itVmd93xovn/0Bdk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			R48UrGIb7F6BvLPVF2P48jRda/8=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			NAQKVueUOqgIwDVCtOGT2/4E0vLbB9qItdjWuQ8Y4u0=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			5WYt2hhEMANx3aTk7Uu4JhTYMQkQNSw58XKM5RYbr6s=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Brw5NvufHt9GeBIHry5KU0S1165BJX6rDg1oGyd2I8c=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			OaMNb5OUBF4KzgvK/QVzVhkIW09rx580qQQOQKIUhAA=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7PyoMbjlTzt9RMHov0C8VhHB5HhFRxroi9IR4Cty+kk=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFo3i1tmgmyD6qiMTnV8l9zsllWcd4lhy0ZG2k52Bk=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1SzxN4QRQ1sj9B4edDvk+6cBHOSCx+38BjJiFcNp0jQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			SQQBiAx4bcVmkhziEv2GiN7BWTzM4/URZLpIPFhVkBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			H1wnyYDv3CZLtAE23ag8Lbd7BbRMwpHrKANNpbzdYQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			KPIzps//u3nn7FQgdoF+sjIOkV1tGdfDVpv+Lst5NxA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			x3YYLMHFl5kOG5NAWiCY2cqFUSu4EiEP0Ngw+Mp5YEc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mwC7WMYupYc97/JPiVbMaV5NLcVzOlQp+XCUVVHHJKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			iOSCWP0NkBOlrvk+o7Yf1CE7CV8eF/HSiIOLOGjt6Z8=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			keT+Kvg6SG3McssA7zeLiWt5nT4lEDAqSYGEXnLHOd4=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			M8BnFla13d5e6axiW/ENVj9X4URt8tjZxU6xGBd7qFo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ht/dNV7S3xIFXFzuiz7A47qJjs+TfuR7ShJWExrTPyM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			jgyBXk9YOKDYr4StcYX4o4EOZojzj5aLdWVoahqfB1Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vKnlI0xiJUC/8z+5C91HsHjdDUv1Oyjhg4zr7yeqq8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			hh6dsqxoCne96/mgokakX+G6/IYOT5Y16RTdVVCJM6c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			SwgKAqFpm3pp0SEJqgEE5iR2uqJzTxbNbhViW+A0Uso=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AkkWpKvl/uu1w/qvPDIsKy5RMTPp6XKvcQsXqVxGZdk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vPjMhtRPd2v0nmHHy+0TM+3fUND6+Wis5/EcU8VlxuE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			/Oa+wpDbmFBOiMns+iTPapDxoa+J6h0/niWnn4z3GEo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6M6n3PGu0CKotsbXo1h8MwXZH0d2F7RAtsWTWLHSSSI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZOQIrFN/mWaQT/A0vgN/pLbF1mja1g6cMUx5GhDc5uU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			fUcwV6knxNLLuGJTt9FGCKYMJTvVtMikQtztQO5lcdw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Im6Gv4K4PkkY/rxSObDmBsUb0mmcTZYRkDq7lnKh3Dg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			IeaDt6SsdzfgcmQwzz/YwosdDmZBzM1bCHaL/T03FNQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			4G5KPn8Go7DM/pM+n9cvc+UHZYSWXTzmeG9enSAoalo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			iBpg2PISG4dQNwVBd51mf1576O72Rc+KjYlQOGNqm+w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2MZ2jSx5vnugwff3U/I3JiZ4EY2CKU0BTzH6Y01JS6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HKcZVcbH8mg9ZBQBRJuMf48X2hRfTU9knIGYHf3i+9U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			e/lsGTvvvyNRRAH49laAdkUK3lLdFZW4Xk38895fb7k=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ojipYWQ+c87NzoqEK7HfRafri4bZzFQvccOtOO1I0UY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			asKtUglhvDjixcPFxvFiCEZvAETHo8YzljspceQBYyY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			aZgyK1yaFe+aiDvBS6oLAYOdJgGVH/xPt3ntD6bIEro=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GZKXX4wwD7EXTC+6G75hMOCUgaz/MI+fuSw9Hi6zFQw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			+wXZ06COBLSEPWp8UR6mNtOe3rdC3k4WhmbWVkrni1c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			spzydEeYGXDe/1xlDU7Hs/d3k9mA802VtfWSaFJLDZ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			kNx6qe/F724tjWMzxJZAd/yZJY9ksA5ijVDbOZm7Sug=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GvB5ScNRfJyAASX/4GaMktd/HWoqdch6yKab6mt0VO8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Y7Ctim4nktOrhIcLibZDdX+Sm8ULvs2zozrg7xGIf5A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8kBkTzwACQx1PQjynzCQ0YsPvNCpXPh6TsLNrNPIdlQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Of1c+zcGcj1EcUNu+Ef6kGYr0/Eq/ZYvPO28lVfh/EE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xiWhw6M9cjn2GObVaERUjG8jDOWfvLut6ECskc72cGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			FF0uC5IExwpmoqAFzW+FLTOGm/lyOlNc4WgBnc6Q9MM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xkiax5rkVRamGQBNqEg9FTXjXteflss2O03OdRcOoU8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			tpo4+T3cMyNFyzzNe+baFfM2DhIly5gsjgzYjHdaSxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6qB1DeNy81naKCD66vJP3pOjuzsiqgHBWD0SYBne6mg=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0AKKAjZ86AVIuEZgW6eaND8ig3tGJdKm/Mjh6wGLh8s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nxHvj3blGKUAeMZcqF3QhBxtPOrV7ZoJMLZ55Ha314g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XqWw6Cv2bBXz1Fbq+cGi7bkdAZWpeZB9wzsLUTzd7/s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WDTyrx3plB6e0BddD6facZ0fUznlPH9X81qG9iIwh2M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			tQ7v2+5cT3yEN7R6e7IquZ1gLqgPZA/jmCf95uCfyA8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			FdynpWtfZNtLX9Vqsm6dHfnw7ufriBtya6kfGEvuuLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6FFNtcdvmjvW6ZGxPdvzzRu0pBUoiVkQESacdYBSrrc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			S458uSl6JVO5NTI40Wna3Tgsa7iykabmKrVulR8UXEc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RlZNvEBNyNvymCd5VkkhEz19vZiWUizh+zjgyoo2Y5Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VOcMGJwhrXbHXcONXRvYjXnbzGXKGPoISeMpkRSNWt4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			7lHUYGjkszxBkSNTjtAVyG4a5KoXu9c1mQrzIAlsPVI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			iS6bg7Sl2416WA7HEy2bONaifk13Ch3O8etW7DTTByM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6fJ/FqR0BqogWs1cSRH4eDuJ+um6PaU/nxX5PDvEVl0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			7SeJqOjMlztdaoxvXPJpwY4+vbQTBDkT+P11wT8Hw/s=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GjEZYIGCBWm+taOwzGxVunszbRZeX5vXDKX9duhBzv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vv48/Egz1qEJ1RP9m4pKDblaIB1eNsXemppdfC2Mlf0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Xl3EP6xcI8aISI+iml7VRSaX+9DB9d/bdqJ4Z0rocz0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
