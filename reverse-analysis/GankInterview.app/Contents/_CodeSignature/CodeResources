<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/app-update.yml</key>
		<data>
		GIemMr1++fOFzN535jwmwKuztd8=
		</data>
		<key>Resources/app.asar</key>
		<data>
		NLEAP5Z+E6jiMzwUfXvUQe+DskU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/LICENSE</key>
		<data>
		UR831R05My2UI6kuTNEkGZOYnwM=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/build/Release/bufferutil.node</key>
		<data>
		DNqfLub+xI/nT8oSxEQhWIauhNg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/fallback.js</key>
		<data>
		LgifrPei3xHlD2k1ZcgQud4OQJU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/index.js</key>
		<data>
		oRMU+s8Wzrojq/VWz9OhMmSj45A=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/package.json</key>
		<data>
		KbdxPF3DO9VF5mEh8xH3clVWVWo=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/darwin-x64+arm64/bufferutil.node</key>
		<data>
		Z4/OyzqHvP+9ZpwED4be2JPGdlU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/linux-x64/bufferutil.node</key>
		<data>
		t70LaPLQPfhWb37tAi4grOaSAyw=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/win32-ia32/bufferutil.node</key>
		<data>
		V00ZHt8Scv/5XwIh3+5Ulfq82Pc=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/win32-x64/bufferutil.node</key>
		<data>
		y4GGymuympgClkC/h8vESuHend8=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/src/bufferutil.c</key>
		<data>
		CmpYjwynbXBnSUKcNDR5VwAiCGk=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/LICENSE</key>
		<data>
		hmYvEM5ki/mZBjTG5O7xJ31f/W8=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/build/Release/uiohook_napi.node</key>
		<data>
		3TnbrizHpWfuvzdMk+PyZdmHOSg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/index.js</key>
		<data>
		sZq7+xKMD0fHgqKTX/lQBpV+c7k=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/index.js.map</key>
		<data>
		OPImWW3hYPFowvxP5sx+nLvkoSs=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/prebuild-test-noop.js</key>
		<data>
		W/z32PjqHIVmShvvg9qe3hkeEhw=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/prebuild-test-noop.js.map</key>
		<data>
		LABP5EkshsBNrdfI4X7ZIQKewYg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/COPYING.LESSER.md</key>
		<data>
		a0FYqzpuS7TVd2+rJd4ZsO9xD+s=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/COPYING.md</key>
		<data>
		9g6MdbHz3/VPTQBmgwJ2VihbaRg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/README.md</key>
		<data>
		8aEizq0uOrjXaHI10Ntg2oL0OBQ=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/include/uiohook.h</key>
		<data>
		dTEeHAsZh4rqPlq1UEi3u7gdBTY=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_helper.c</key>
		<data>
		tSgozIhSRpQGHysGsHFlIAtuF2Y=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_helper.h</key>
		<data>
		Z7qYq7oF/SaeQfRGgmK10mGphf8=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_hook.c</key>
		<data>
		PPXmpvJe61UY1Xd6Hs4H6oFLcmI=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/post_event.c</key>
		<data>
		V8RoA8o20+3/xQbo3BsGSqWtpF4=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/system_properties.c</key>
		<data>
		A3zEJiQ45msqAJJx+VlGeJ6D64w=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/logger.c</key>
		<data>
		Zhv82/At5lhYLP+EMJlLchDT1go=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/logger.h</key>
		<data>
		cFt6bjfTUYim02ZEJYEhL1y0CQg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_helper.c</key>
		<data>
		YbzUz66lcJU711MjH6YwX3mdm/k=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_helper.h</key>
		<data>
		COx/yIJ4eHRHQsAZXmtbFV/YAL4=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_hook.c</key>
		<data>
		oESQSlHrbT7KiHHg32W08W8j8xo=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/post_event.c</key>
		<data>
		Wbj3idTu58RpCMZT0ntjQRhK5Rs=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/system_properties.c</key>
		<data>
		DPlnFcBhU2wnLw6vCISsEf5ahmU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_helper.c</key>
		<data>
		/fTxUzfeF5bxSDy55TMmgfSWGwk=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_helper.h</key>
		<data>
		JrFufRHcubOXZ8Q8/1mXA2jlp40=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_hook.c</key>
		<data>
		G2e6V2jfbhYlPvUd0PQYblYtG2Y=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/post_event.c</key>
		<data>
		ZtrPGc/HXBk9Dsd+4wWuIrDQo2Q=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/system_properties.c</key>
		<data>
		qR2G7j7qqmohQBf8GEaMPaI6oiw=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/package.json</key>
		<data>
		c4AyE5NQB+eeCEJS7H6KTAW30LU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/darwin-arm64/node.napi.node</key>
		<data>
		pZtmAYoWuBPhfW1Iv87gX1xZYgo=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/darwin-x64/node.napi.node</key>
		<data>
		1zogASXe/TzpbFPaaY1BUeIzYaU=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/linux-arm64/node.napi.node</key>
		<data>
		aOSZjULOxAFk/6ZC/9H18fkpyQ4=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/linux-x64/node.napi.node</key>
		<data>
		aOSZjULOxAFk/6ZC/9H18fkpyQ4=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/win32-x64/node.napi.node</key>
		<data>
		O1bQxLeHqDiXLZowSs3x1fK0nwA=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/addon.c</key>
		<data>
		p1+N8QFqa+O+xjDBqhbmEDUIwz0=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/napi_helpers.c</key>
		<data>
		6KxwoOlkuJ80Pp1oIJ/5fxI11Pw=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/napi_helpers.h</key>
		<data>
		omN+wpCUaJrNzlcjclUPU/zrSNg=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/uiohook_worker.c</key>
		<data>
		7YA2ohmfmByNj7s2rtLPSlSNjYo=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/uiohook_worker.h</key>
		<data>
		FEfp7Iuygan+k29MHkSD9NTHkwY=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/LICENSE</key>
		<data>
		q0unY91n6leiMx+cE2V0Kg09yU4=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/build/Release/validation.node</key>
		<data>
		QZFp9zgQCJ8WcT0ljO8dKOgI2po=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/fallback.js</key>
		<data>
		tDOzeahq/VCXwwsTqH5T/bUg4yE=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/index.js</key>
		<data>
		oRMU+s8Wzrojq/VWz9OhMmSj45A=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/package.json</key>
		<data>
		rFmldVEjEQHiT4AoVWejBQMxsQk=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/darwin-x64+arm64/node.napi.node</key>
		<data>
		oEwBQUpzftgGIxr8JkW85pcg8FM=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/linux-x64/node.napi.node</key>
		<data>
		zlD9wL99GJPbp6dCLKyZ8VHXWLo=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/win32-ia32/node.napi.node</key>
		<data>
		wTN/HT0h6lfqMCm6RZievAdJIL0=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/win32-x64/node.napi.node</key>
		<data>
		riscRlKuxzFWB/xBOkwljxG2lUQ=
		</data>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/src/validation.c</key>
		<data>
		8enQySUXpku/YbED8H5EKECbqnA=
		</data>
		<key>Resources/bin/macos-arm64/SharpHookProtector</key>
		<data>
		5jWAxbUdo0RXezsKKjXmZXUejkI=
		</data>
		<key>Resources/bin/macos-arm64/libuiohook.dylib</key>
		<data>
		1107AIW7FbDTBlAiNPtyEIK117w=
		</data>
		<key>Resources/bin/macos-x64/SharpHookProtector</key>
		<data>
		HZzvFGe9ycp4pCXzmlCaPoB8N6Q=
		</data>
		<key>Resources/bin/macos-x64/libuiohook.dylib</key>
		<data>
		GgUtluQ9NlsxDnDrW6G9ev6DHyM=
		</data>
		<key>Resources/bin/macos/system-audio-capture</key>
		<data>
		+cTMgsOTldoTfatae0fEWxTaGSs=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		0RS1d9g/1JXFswunnunKjRNi0cY=
		</data>
		<key>Resources/resources/start-en.wav</key>
		<data>
		gzEReto2MYbX644XbOp7ziFUx+E=
		</data>
		<key>Resources/resources/start-zh.wav</key>
		<data>
		d7AMjUgPokwztnn/joCLfZGz0UM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Electron Framework.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			CV1Ind3yfDLXHduzOLaZfG2lVIc=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Electron.framework" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/GankInterview Helper (GPU).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			sFIel16ZPBx1J4eNQs8+roKCjrk=
			</data>
			<key>requirement</key>
			<string>identifier "com.minifold.gankinterview.helper.GPU" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/GankInterview Helper (Plugin).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			HAUEpz6KiYDVKaJ23UylKPFgvcI=
			</data>
			<key>requirement</key>
			<string>identifier "com.minifold.gankinterview.helper.Plugin" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/GankInterview Helper (Renderer).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			UyZSVVxu0o3SUsudtKAQxSSJumY=
			</data>
			<key>requirement</key>
			<string>identifier "com.minifold.gankinterview.helper.Renderer" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/GankInterview Helper.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			qXhRCbpg1QbckcmaNyISNGXvctA=
			</data>
			<key>requirement</key>
			<string>identifier "com.minifold.gankinterview.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/Mantle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			90dHyhTRcqFK40/9Nywyi9U9ayg=
			</data>
			<key>requirement</key>
			<string>identifier "org.mantle.Mantle" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/ReactiveObjC.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			AaZqo/HWwfSnoS+VtK/S9TbIeSo=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.reactive" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Frameworks/Squirrel.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			pbscLV4eDZ4rU5UUIbZutvaCBHs=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Squirrel" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = "5362N878N7"</string>
		</dict>
		<key>Resources/app-update.yml</key>
		<dict>
			<key>hash2</key>
			<data>
			1XO9sTG6WeqZLT7Q8J8Fyb/yg9hGG3eJ90sk6h4frbA=
			</data>
		</dict>
		<key>Resources/app.asar</key>
		<dict>
			<key>hash2</key>
			<data>
			wvxjD9SiAN9P9jHeFN1mrTO0ZAjC7de7gOt7kSTgcSA=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			Kync/g1kcffoySxfs4yfk+3uEDMJNwVUQBkvGDKx7O8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/build/Release/bufferutil.node</key>
		<dict>
			<key>hash2</key>
			<data>
			DRnthLvD73VJTVh3tv5Gm1XZ93zfLsDfHd3CCHTO9Dk=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/fallback.js</key>
		<dict>
			<key>hash2</key>
			<data>
			9KZdGpjbSUOWFhGfugm+DZpCF69XshKCq3uRyLWggrk=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/index.js</key>
		<dict>
			<key>hash2</key>
			<data>
			cIRqQ8K9OxB3RhVjGU5NJAexjn12fW/YeF6pt9bd0EQ=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			peNQEFCWWZo2oZzYhMxx0N4rX67T2Ghs/KlMKO6c9eY=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/darwin-x64+arm64/bufferutil.node</key>
		<dict>
			<key>hash2</key>
			<data>
			cLLsq1kJ64IjxyD6hvNXBeDdu8kwwqtA/0tdBrJSSEU=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/linux-x64/bufferutil.node</key>
		<dict>
			<key>hash2</key>
			<data>
			LvsgKkfYuqaIe432dylGnq4AF9T4k4hcQkAu7nS9YLI=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/win32-ia32/bufferutil.node</key>
		<dict>
			<key>hash2</key>
			<data>
			Shdar/lI+Zdn+0kFmM0BykMiPjChi9J6Zs6NuI/hgkc=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/prebuilds/win32-x64/bufferutil.node</key>
		<dict>
			<key>hash2</key>
			<data>
			Sm6++7gp2tFJazEElX1W1CgqeyUkDZ+E8R2ccYoB2NM=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/bufferutil/src/bufferutil.c</key>
		<dict>
			<key>hash2</key>
			<data>
			9RkVY2plUc0YCn+GYR/e2UL98vWU97opXcdWT6p0GI0=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			Bxwic3xF53Yh6zeNsiHr+Iz4YThHQ3HbzAY4LWYGmA8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/build/Release/uiohook_napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			3jLR0ddcmBW5HH+pwayL28PhiTdrnWxgpxM0Db8pd84=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/index.js</key>
		<dict>
			<key>hash2</key>
			<data>
			49c5RqSGuQ3H0wT5gEWLpP1Ju8MlUcK0DEk/qv/uSlU=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/index.js.map</key>
		<dict>
			<key>hash2</key>
			<data>
			4qMMuCLVDiJUEatV/5yiA4q09zJ0aUUgkJLqBi2llLU=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/prebuild-test-noop.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mnsFDiaw7gB26+vntE53nRtLkNUDMVUDFrR7odgrjPw=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/dist/prebuild-test-noop.js.map</key>
		<dict>
			<key>hash2</key>
			<data>
			pM5/ydjOzaqek7Zxd0Dpz4P1iW8Np9IddNtDjdIBo2M=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/COPYING.LESSER.md</key>
		<dict>
			<key>hash2</key>
			<data>
			etfNRoMS71jkeOrAJykGPdB6sjIMNa3AjCrQD4QLkjc=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/COPYING.md</key>
		<dict>
			<key>hash2</key>
			<data>
			cZdyZ3I4HDXIcHm9hy42cfbENGZJg2j4hWMd6zUcoiE=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			uZCROvS1DJOmmLp8Q2g3SNh7QPW+jNQAjOdopQNisp4=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/include/uiohook.h</key>
		<dict>
			<key>hash2</key>
			<data>
			91LXS4Vy4fuDOpRANBpFpMQ2SjVP5if29kW5Wc2Je4Y=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_helper.c</key>
		<dict>
			<key>hash2</key>
			<data>
			sL961CMWOFiayWRUBEqOws1UXfFSYTwEdaxoqav1R4A=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_helper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kwZOiMMuvlfHK5Sz2kAVypQNJcXi1BVo63sSTBP8gW4=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/input_hook.c</key>
		<dict>
			<key>hash2</key>
			<data>
			WP1LNqzgkVWe/oAHva/iaxqbq5kSwoQVDLcdmk/7cas=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/post_event.c</key>
		<dict>
			<key>hash2</key>
			<data>
			Z5fyHI3Y8zcX8jk9WVdFUcqbP8b8Ocakz9+RoEocPvU=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/darwin/system_properties.c</key>
		<dict>
			<key>hash2</key>
			<data>
			SzJWVsYxkBPQQ4ClL/qdH8V8/eEKkKn6Qt0HvXL7qfs=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/logger.c</key>
		<dict>
			<key>hash2</key>
			<data>
			fJgNOgWQCYtoK0Z6b3iXUbxvMGWNnzp2HCgPjFc7JY0=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/logger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YKQyA3vFSI+1+CohQxXsuT4kJhyBNjzkHpcxOPqOLPc=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_helper.c</key>
		<dict>
			<key>hash2</key>
			<data>
			5uchLoe3eWp9Q4dyjZkcnD4Yj4mKe5783MXBsTxTe0E=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_helper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			BaZyiZbKGthpmyOYsoShHjdJFBR11MuE3QrAsST7wug=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/input_hook.c</key>
		<dict>
			<key>hash2</key>
			<data>
			6g6BA0m+cFXobBg3NsXCaSUBz6tLFmVpjzgGZRmg8e8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/post_event.c</key>
		<dict>
			<key>hash2</key>
			<data>
			QBGQBv1e1nYITPpaCl/4hB7N4VwyaRfmAVE5SfvEDEw=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/windows/system_properties.c</key>
		<dict>
			<key>hash2</key>
			<data>
			KU3Mgyy7ZEY/o36WvzUkCjubGBS03/fZ95wnx4+tHj8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_helper.c</key>
		<dict>
			<key>hash2</key>
			<data>
			pUCptGCcq425vXUloSo77rRAHnCkn14EB2zDfGzsixY=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_helper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SwK51p631Ds8m021GXx3o2usUKA8Cnwyq7oJa7ScBfg=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/input_hook.c</key>
		<dict>
			<key>hash2</key>
			<data>
			/XftQwWfFcKfndFAEENu2US4TxI0EEtFtwFQZU2DYz8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/post_event.c</key>
		<dict>
			<key>hash2</key>
			<data>
			TTer4P8eFFuQrhW9yQM51iaLt3Fky8UyFNjCwLaUjwI=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/libuiohook/src/x11/system_properties.c</key>
		<dict>
			<key>hash2</key>
			<data>
			ZEtvD1+vC5SLpol82VYVBXn3PdGUDIfKT87p0j3J6Wc=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LN2uaHQ2FxIiTFaTUBySim0/EonJh08Hz5/9h1G6VmQ=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/darwin-arm64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			tFA1sxtcVto0qxzACH20Aq5hjGBNy/qc2cc4iMaGsW8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/darwin-x64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			M+f26+1Vul24iaidVYpx+fpaQ62Nx1EEnfcNHO8Zhz8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/linux-arm64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			80f/fBzbVKIldb8dEHNDC4cJRipKUmG0SjC1vc9xTq0=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/linux-x64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			80f/fBzbVKIldb8dEHNDC4cJRipKUmG0SjC1vc9xTq0=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/prebuilds/win32-x64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			pOGNWJbFiJY17+McbxoM6Myv6r6VIeD5LEalhh6MKQs=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/addon.c</key>
		<dict>
			<key>hash2</key>
			<data>
			6PvOPVTsuSzZCIMrLBK1qiYlxir3Vm5vKwH+NZn2Okc=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/napi_helpers.c</key>
		<dict>
			<key>hash2</key>
			<data>
			BKj23jZsb4LfkakLB7jrwAnvw6Ws4ogbhs3EDmliW+o=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/napi_helpers.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o/s46Do015eOtwDO7lJ8ax5fG4nNIeJfxRbPuwiqfNg=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/uiohook_worker.c</key>
		<dict>
			<key>hash2</key>
			<data>
			vgAmu+A/yq63nbbGNqUl16mNiVkCdnyUYpVr3nOYfI8=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/uiohook-napi/src/lib/uiohook_worker.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9NvDa7r17wTXATZep+cqDwL3dTfp9RfmzGXrwvJ3ags=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			6/d09g3ScyXP1iwjORffzIVR+Aj2nKhla+fR6vE8RPI=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/build/Release/validation.node</key>
		<dict>
			<key>hash2</key>
			<data>
			vzdHu2lVSoU9c5V5WkDSMDzDpK0W4rotJOpAc1/T4jk=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/fallback.js</key>
		<dict>
			<key>hash2</key>
			<data>
			dK0xPkjRAIzyDkrWkfiDMZvouq/0RC2GeWgZ+015ac0=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/index.js</key>
		<dict>
			<key>hash2</key>
			<data>
			cIRqQ8K9OxB3RhVjGU5NJAexjn12fW/YeF6pt9bd0EQ=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ncB5FCIFkkM4wcz6O77d/ldFoZH5bzvG8d9KQjABV4o=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/darwin-x64+arm64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			aEqFyhHomKAEsa6amiM0WIaPzcYRxhfC9CiqeHqQeEU=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/linux-x64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			CMBzeDT7XCyjjv5k4nTBBy1+3RhBUnX54Fc99bzdDic=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/win32-ia32/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			CAirVCxDud+FtmigLwcFUpt7uax54msHdmgmV8YAU1I=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/prebuilds/win32-x64/node.napi.node</key>
		<dict>
			<key>hash2</key>
			<data>
			SLcnX0fNRKBdNJ60/bbPxFHMv2CaSlb6NEUrzyMcEgg=
			</data>
		</dict>
		<key>Resources/app.asar.unpacked/node_modules/utf-8-validate/src/validation.c</key>
		<dict>
			<key>hash2</key>
			<data>
			2PpDrqE8YiBA0csvbQWvDxATvJInwTPsgSM9EijXf1w=
			</data>
		</dict>
		<key>Resources/bin/macos-arm64/SharpHookProtector</key>
		<dict>
			<key>hash2</key>
			<data>
			PvZ1aUjNvH2CaTBku8IEuFNrhIKmi1X7UUyygL3Ocb8=
			</data>
		</dict>
		<key>Resources/bin/macos-arm64/libuiohook.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			JIQgwZxuhvksYiRN4KewQBUDtNAMopHQZv5zuVcLhbo=
			</data>
		</dict>
		<key>Resources/bin/macos-x64/SharpHookProtector</key>
		<dict>
			<key>hash2</key>
			<data>
			bS9SZlX9dNfh4ndzqHUsxKkqa0ZAcn4gbu62zGhn03Q=
			</data>
		</dict>
		<key>Resources/bin/macos-x64/libuiohook.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Ie5ETpIkTpim5gMwGljBWcP32/kifivhie1DXhG9uJ4=
			</data>
		</dict>
		<key>Resources/bin/macos/system-audio-capture</key>
		<dict>
			<key>hash2</key>
			<data>
			gloMC3M2ywGS0XIg58LCyHnB6Xu7vVzNm+r3Rlf15U0=
			</data>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			eEBVTq/LUSLPCZNKC3K9TVVbPPI16ncvoaxXCC+tVzc=
			</data>
		</dict>
		<key>Resources/resources/start-en.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			pa+xbxRoSrDijC3W+X/ODtU9ypd9xHjZVpCP1fbDPmU=
			</data>
		</dict>
		<key>Resources/resources/start-zh.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			hcmArH7sqFQAfVXuazTjx8tmvbUdSdMH3quFd0pJu0k=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
