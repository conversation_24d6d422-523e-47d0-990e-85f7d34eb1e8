# GankInterview 账号管理系统 - 最终版

## 🎯 功能特性

- **自动账号创建** - 批量创建GankInterview账号
- **邮箱验证** - 自动处理临时邮箱验证流程
- **登录状态保存** - 智能session管理，避免重复登录
- **API密钥获取** - 自动获取Azure语音和OpenAI API密钥
- **账号验证** - 验证现有账号状态和API密钥
- **精简日志** - 可配置的日志输出级别

## 📁 文件说明

- `account-manager.js` - 核心账号管理系统
- `accounts.json` - 账号数据存储文件
- `sessions.json` - 登录状态缓存文件
- `README.md` - 使用说明文档

## 🚀 使用方法

### 创建新账号

```bash
# 创建10个账号（默认）
node account-manager.js

# 创建指定数量的账号
node account-manager.js --count=5

# 最小日志输出
node account-manager.js --count=10 --log=minimal
```

### 验证现有账号

```bash
# 验证所有现有账号
node account-manager.js --mode=verify

# 详细日志输出
node account-manager.js --mode=verify --log=verbose
```

## ⚙️ 配置选项

### 命令行参数

- `--mode=create|verify` - 运行模式（创建/验证）
- `--count=数字` - 创建账号数量（仅创建模式）
- `--log=minimal|normal|verbose` - 日志级别

### 日志级别说明

- **minimal** - 只显示成功和错误信息
- **normal** - 显示基本进度信息（默认）
- **verbose** - 显示详细调试信息

## 📊 输出文件

### accounts.json
```json
{
  "accounts": [
    {
      "id": "user_xxx",
      "name": "用户姓名",
      "email": "邮箱地址",
      "password": "密码",
      "voiceTime": 30,
      "azureCredentials": {
        "token": "Azure JWT令牌",
        "region": "eastasia",
        "type": "jwt"
      },
      "openaiCredentials": {
        "apiKey": "OpenAI API密钥",
        "baseURL": "http://*************:7091/v1",
        "userType": "TRIAL"
      }
    }
  ],
  "summary": {
    "totalAccounts": 10,
    "validAccounts": 10,
    "totalVoiceMinutes": 300,
    "totalVoiceHours": 5.0
  }
}
```

### sessions.json
```json
{
  "邮箱地址": {
    "cookies": { "session数据": "..." },
    "expiresAt": "2025-01-29T15:00:00.000Z",
    "userToken": "用户令牌",
    "createdAt": "2025-01-29T14:00:00.000Z"
  }
}
```

## 🔑 API使用示例

### Azure语音服务
```javascript
const account = accounts[0];
const azureToken = account.azureCredentials.token;
const region = account.azureCredentials.region;

// 语音识别请求
fetch(`https://${region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=zh-CN`, {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${azureToken}`,
        'Content-Type': 'audio/wav'
    },
    body: audioBuffer
});
```

### OpenAI API
```javascript
const account = accounts[0];
const openaiKey = account.openaiCredentials.apiKey;
const baseURL = account.openaiCredentials.baseURL;

// 聊天请求
fetch(`${baseURL}/chat/completions`, {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${openaiKey}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'gemini-2.0-flash',
        messages: [{ role: 'user', content: 'Hello!' }]
    })
});
```

## 💡 使用建议

1. **首次运行** - 建议使用 `--log=normal` 查看详细进度
2. **批量创建** - 每个账号间隔3秒，避免请求过快
3. **Session复用** - 系统会自动保存登录状态，避免重复登录
4. **定期验证** - 建议定期运行验证模式检查账号状态
5. **语音时长** - 每个试用账号有30分钟语音时长
6. **API轮换** - 建议轮换使用不同账号的API密钥

## 🔧 故障排除

### 常见问题

1. **注册失败** - 可能是邮箱已存在，系统会自动跳过
2. **验证邮件未收到** - 系统会重试6次，每次间隔8秒
3. **登录失败** - 检查账号密码是否正确
4. **API密钥获取失败** - 可能是网络问题，可重新运行验证模式

### 调试方法

```bash
# 使用详细日志查看问题
node account-manager.js --mode=verify --log=verbose
```

## 📈 性能优化

- **并行API请求** - 同时获取Azure、语音时长、订阅信息
- **Session缓存** - 避免重复登录，提高效率
- **智能重试** - 自动处理临时网络问题
- **内存优化** - 及时清理不需要的数据

## 🛡️ 安全说明

- 账号密码使用随机生成，确保安全性
- Session数据本地存储，不会泄露
- API密钥仅用于合法用途
- 建议定期更新和轮换使用

---

**版本**: 1.0.0  
**更新时间**: 2025-01-29  
**兼容性**: Node.js 14+
