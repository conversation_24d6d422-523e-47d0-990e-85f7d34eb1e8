#!/usr/bin/env node

/**
 * GankInterview 账号管理系统 - 最终版
 * 功能：账号创建、验证、登录状态保存、API密钥获取
 */

import https from 'https';
import http from 'http';
import fs from 'fs';
import path from 'path';
import zlib from 'zlib';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AccountManager {
    constructor(options = {}) {
        this.options = {
            targetAccounts: options.targetAccounts || 10,
            logLevel: options.logLevel || 'normal', // 'minimal', 'normal', 'verbose'
            ...options
        };
        
        // 中文姓氏和名字库
        this.surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗'];
        this.givenNames = ['明轩', '雨涵', '浩然', '思琪', '志强', '美丽', '建华', '秀英', '伟', '芳', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '娟', '涛', '明', '超', '秀兰', '霞', '平', '刚', '桂英'];
        
        // 支持的邮箱域名
        this.emailDomains = [
            'sharklasers.com', 'guerrillamail.info', 'grr.la', 'guerrillamail.biz',
            'guerrillamail.com', 'guerrillamail.de', 'guerrillamail.net', 
            'guerrillamail.org', 'pokemail.net', 'spam4.me'
        ];
        
        this.accountsFile = path.join(__dirname, 'accounts.json');
        this.sessionsFile = path.join(__dirname, 'sessions.json');
        this.usedEmails = new Set();
        this.usedNames = new Set();
        this.savedSessions = this.loadSessions();
        
        this.results = {
            accounts: [],
            totalVoiceTime: 0,
            validAccounts: 0,
            errors: []
        };
    }

    /**
     * 精简日志输出
     */
    log(message, type = 'info', accountName = null) {
        if (this.options.logLevel === 'minimal' && type !== 'success' && type !== 'error') {
            return;
        }
        
        const timestamp = new Date().toLocaleTimeString();
        const prefix = accountName ? `[${accountName}]` : '';
        const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'info' ? '📋' : '🔄';
        
        console.log(`[${timestamp}] ${icon} ${prefix} ${message}`);
    }

    /**
     * Session管理
     */
    loadSessions() {
        try {
            if (fs.existsSync(this.sessionsFile)) {
                return JSON.parse(fs.readFileSync(this.sessionsFile, 'utf8'));
            }
        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`加载session失败: ${error.message}`, 'error');
            }
        }
        return {};
    }

    saveSessions() {
        try {
            fs.writeFileSync(this.sessionsFile, JSON.stringify(this.savedSessions, null, 2));
        } catch (error) {
            this.log(`保存session失败: ${error.message}`, 'error');
        }
    }

    /**
     * 加载现有账号
     */
    loadExistingAccounts() {
        const accounts = [];

        try {
            if (fs.existsSync(this.accountsFile)) {
                const data = JSON.parse(fs.readFileSync(this.accountsFile, 'utf8'));
                if (data.accounts && data.accounts.length > 0) {
                    accounts.push(...data.accounts);
                }
            }
        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`加载账号失败: ${error.message}`, 'error');
            }
        }

        // 添加手动注册的主账号（如果不存在）
        const manualAccount = {
            id: 'manual_main_account',
            name: '主账号',
            email: '<EMAIL>',
            password: 'rampant.grouper',
            source: 'manual'
        };

        const existingEmails = new Set(accounts.map(acc => acc.email));
        if (!existingEmails.has(manualAccount.email)) {
            accounts.push(manualAccount);
        }

        return accounts;
    }

    /**
     * 验证现有账号
     */
    async verifyAccount(account) {
        try {
            if (this.options.logLevel !== 'minimal') {
                this.log(`验证账号: ${account.name} (${account.email})`, 'info');
            }

            const sessionData = await this.getOrCreateSession(account);
            if (!sessionData.success) {
                return {
                    ...account,
                    loginSuccess: false,
                    error: sessionData.error
                };
            }

            const accountInfo = await this.getAccountCredentials(account, sessionData);
            accountInfo.sessionFromCache = sessionData.fromCache;

            return accountInfo;

        } catch (error) {
            this.log(`验证账号异常: ${error.message}`, 'error', account.name);
            return {
                ...account,
                loginSuccess: false,
                error: error.message
            };
        }
    }

    /**
     * 验证现有账号列表
     */
    async verifyAccounts(accounts) {
        console.log(`🔍 开始验证 ${accounts.length} 个账号\n`);

        for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i];
            this.log(`验证第 ${i + 1}/${accounts.length} 个账号`, 'info');

            try {
                const result = await this.verifyAccount(account);
                this.results.accounts.push(result);

                if (result.loginSuccess) {
                    this.results.validAccounts++;
                    this.results.totalVoiceTime += result.voiceTime || 0;
                    this.log(`账号验证成功: ${result.name}`, 'success');
                } else {
                    this.results.errors.push(`${result.name}: ${result.error}`);
                    this.log(`账号验证失败: ${result.name}`, 'error');
                }

                if (i < accounts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }

            } catch (error) {
                this.log(`验证异常: ${error.message}`, 'error');
                this.results.errors.push(`验证异常: ${error.message}`);
            }
        }
    }

    /**
     * 获取或创建登录session
     */
    async getOrCreateSession(account) {
        const sessionKey = account.email;
        const existingSession = this.savedSessions[sessionKey];

        if (existingSession && this.isSessionValid(existingSession)) {
            if (this.options.logLevel === 'verbose') {
                this.log(`使用缓存session`, 'info', account.name);
            }
            return {
                success: true,
                cookies: new Map(Object.entries(existingSession.cookies)),
                fromCache: true
            };
        }

        return await this.createNewSession(account);
    }

    isSessionValid(sessionData) {
        if (!sessionData || !sessionData.expiresAt) {
            return false;
        }

        const now = Date.now();
        const expiresAt = new Date(sessionData.expiresAt).getTime();
        return (expiresAt - now) > 5 * 60 * 1000; // 提前5分钟过期
    }

    async createNewSession(account) {
        try {
            const cookies = new Map();

            const loginPageResponse = await this.request('https://www.gankinterview.cn/auth/login');
            this.processCookies(loginPageResponse.cookies, cookies);

            const loginData = JSON.stringify({
                email: account.email,
                password: account.password,
                callbackUrl: '/app'
            });

            const loginResponse = await this.request('https://www.gankinterview.cn/api/auth/sign-in/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://www.gankinterview.cn/auth/login',
                    'Origin': 'https://www.gankinterview.cn'
                },
                cookies: this.cookiesToString(cookies),
                body: loginData
            });

            this.processCookies(loginResponse.cookies, cookies);

            if (loginResponse.statusCode === 200) {
                const loginResult = JSON.parse(loginResponse.body);
                if (loginResult.token || !loginResult.error) {
                    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString();
                    this.savedSessions[account.email] = {
                        cookies: Object.fromEntries(cookies),
                        expiresAt: expiresAt,
                        userToken: loginResult.token,
                        createdAt: new Date().toISOString()
                    };

                    this.log(`登录成功`, 'success', account.name);
                    return {
                        success: true,
                        cookies: cookies,
                        userToken: loginResult.token,
                        fromCache: false
                    };
                } else {
                    this.log(`登录失败: ${loginResult.error}`, 'error', account.name);
                    return { success: false, error: loginResult.error };
                }
            } else {
                this.log(`登录失败: HTTP ${loginResponse.statusCode}`, 'error', account.name);
                return { success: false, error: `HTTP ${loginResponse.statusCode}` };
            }

        } catch (error) {
            this.log(`登录异常: ${error.message}`, 'error', account.name);
            return { success: false, error: error.message };
        }
    }

    /**
     * HTTP请求封装
     */
    async request(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const requestModule = isHttps ? https : http;

            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'application/json, text/html, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    ...options.headers
                }
            };

            if (options.cookies) {
                requestOptions.headers['Cookie'] = options.cookies;
            }

            if (options.body) {
                requestOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
            }

            const req = requestModule.request(requestOptions, (res) => {
                let stream = res;
                const encoding = res.headers['content-encoding'];

                if (encoding === 'gzip') {
                    stream = res.pipe(zlib.createGunzip());
                } else if (encoding === 'deflate') {
                    stream = res.pipe(zlib.createInflate());
                } else if (encoding === 'br') {
                    stream = res.pipe(zlib.createBrotliDecompress());
                }

                let data = '';
                stream.on('data', chunk => data += chunk);
                stream.on('end', () => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        cookies: res.headers['set-cookie'] || []
                    });
                });

                stream.on('error', reject);
            });

            req.on('error', reject);

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    }

    /**
     * Cookie处理
     */
    processCookies(cookieHeaders, cookieMap) {
        if (cookieHeaders) {
            cookieHeaders.forEach(cookie => {
                const [nameValue] = cookie.split(';');
                const [name, value] = nameValue.split('=');
                if (name && value) {
                    cookieMap.set(name.trim(), value.trim());
                }
            });
        }
    }

    cookiesToString(cookieMap) {
        return Array.from(cookieMap.entries())
            .map(([name, value]) => `${name}=${value}`)
            .join('; ');
    }

    /**
     * 生成全随机密码
     */
    generateRandomPassword(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********!@#$%^&*';
        let password = '';

        // 确保包含至少一个大写字母、小写字母、数字和特殊字符
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
        password += '**********'[Math.floor(Math.random() * 10)];
        password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

        // 填充剩余长度
        for (let i = 4; i < length; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }

        // 打乱密码字符顺序
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    /**
     * 生成随机用户信息（使用全随机密码）
     */
    async generateRandomUser(accountIndex = 0, maxRetries = 5) {
        for (let retry = 0; retry < maxRetries; retry++) {
            const surname = this.surnames[Math.floor(Math.random() * this.surnames.length)];
            const givenName = this.givenNames[Math.floor(Math.random() * this.givenNames.length)];
            const fullName = surname + givenName;

            if (this.usedNames.has(fullName)) {
                continue;
            }

            const domainIndex = accountIndex % this.emailDomains.length;
            const selectedDomain = this.emailDomains[domainIndex];
            const emailInfo = await this.getTempEmail(selectedDomain);

            if (this.usedEmails.has(emailInfo.email)) {
                continue;
            }

            this.usedNames.add(fullName);
            this.usedEmails.add(emailInfo.email);

            // 生成全随机密码
            const password = this.generateRandomPassword(16);

            return {
                name: fullName,
                email: emailInfo.email,
                emailInfo: emailInfo,
                password: password,
                id: `user_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
                accountIndex: accountIndex,
                retry: retry,
                createdAt: new Date().toISOString()
            };
        }

        throw new Error(`经过${maxRetries}次尝试，无法生成唯一的用户信息`);
    }

    /**
     * 获取临时邮箱
     */
    async getTempEmail(preferredDomain = null) {
        try {
            const selectedDomain = preferredDomain || this.emailDomains[Math.floor(Math.random() * this.emailDomains.length)];

            const response = await this.request('https://www.guerrillamail.com/');
            const cookies = new Map();
            this.processCookies(response.cookies, cookies);

            if (response.statusCode === 200) {
                const emailMatches = response.body.match(/([a-zA-Z0-9+._-]{5,})@(sharklasers\.com|guerrillamail\.[a-z]+|grr\.la|pokemail\.net|spam4\.me)/g);

                if (emailMatches && emailMatches.length > 0) {
                    const currentEmail = emailMatches[0];
                    const [username, currentDomain] = currentEmail.split('@');

                    // 过滤系统邮箱
                    if (username.includes('no-reply') || username.includes('admin') || username.includes('support')) {
                        for (let i = 1; i < emailMatches.length; i++) {
                            const altEmail = emailMatches[i];
                            const [altUsername] = altEmail.split('@');
                            if (!altUsername.includes('no-reply') && !altUsername.includes('admin') && !altUsername.includes('support')) {
                                const finalEmail = selectedDomain !== currentDomain ? `${altUsername}@${selectedDomain}` : altEmail;
                                return {
                                    email: finalEmail,
                                    domain: selectedDomain,
                                    username: altUsername,
                                    cookies: cookies,
                                    sessionEstablished: true
                                };
                            }
                        }
                    } else {
                        const finalEmail = currentDomain !== selectedDomain ? `${username}@${selectedDomain}` : currentEmail;
                        return {
                            email: finalEmail,
                            domain: selectedDomain,
                            username: username,
                            cookies: cookies,
                            sessionEstablished: true
                        };
                    }
                }
            }
        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`获取临时邮箱失败: ${error.message}`, 'error');
            }
        }

        // 备选方案：生成随机邮箱
        const selectedDomain = preferredDomain || this.emailDomains[Math.floor(Math.random() * this.emailDomains.length)];
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substring(2, 8);
        const randomEmail = `${randomStr}${timestamp}@${selectedDomain}`;

        return {
            email: randomEmail,
            domain: selectedDomain,
            username: randomEmail.split('@')[0],
            cookies: new Map(),
            sessionEstablished: false
        };
    }

    /**
     * 获取账号API密钥
     */
    async getAccountCredentials(userInfo, loginData) {
        try {
            const [azureResponse, voiceTimeResponse, subscriptionResponse] = await Promise.all([
                this.request('https://www.gankinterview.cn/api/user/azure-stt-token', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': 'https://www.gankinterview.cn/app'
                    },
                    cookies: this.cookiesToString(loginData.cookies)
                }),
                this.request('https://www.gankinterview.cn/api/user_rights/voice-remaining-time', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': 'https://www.gankinterview.cn/app'
                    },
                    cookies: this.cookiesToString(loginData.cookies)
                }),
                this.request('https://www.gankinterview.cn/api/user/subscription', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': 'https://www.gankinterview.cn/app'
                    },
                    cookies: this.cookiesToString(loginData.cookies)
                })
            ]);

            const accountData = {
                id: userInfo.id,
                name: userInfo.name,
                email: userInfo.email,
                password: userInfo.password,
                registrationSuccess: true,
                loginSuccess: true,
                voiceTime: 0,
                azureCredentials: null,
                openaiCredentials: null,
                subscriptionType: null,
                createdAt: new Date().toISOString()
            };

            // 处理Azure凭据
            if (azureResponse.statusCode === 200) {
                try {
                    const azureData = JSON.parse(azureResponse.body);
                    if (azureData.token) {
                        accountData.azureCredentials = {
                            token: azureData.token,
                            region: azureData.region || 'eastasia',
                            type: 'jwt'
                        };
                        this.log(`Azure令牌获取成功`, 'success', userInfo.name);
                    }
                } catch (e) {
                    this.log(`Azure令牌解析失败`, 'error', userInfo.name);
                }
            }

            // 处理语音时间
            if (voiceTimeResponse.statusCode === 200) {
                try {
                    const voiceData = JSON.parse(voiceTimeResponse.body);
                    accountData.voiceTime = voiceData.remainingTimeMinutes || voiceData.remainingTime || 0;
                    this.log(`语音时间: ${accountData.voiceTime}分钟`, 'success', userInfo.name);
                } catch (e) {
                    this.log(`语音时间解析失败`, 'error', userInfo.name);
                }
            }

            // 处理订阅信息
            if (subscriptionResponse.statusCode === 200) {
                try {
                    const subData = JSON.parse(subscriptionResponse.body);
                    if (subData.subscription) {
                        accountData.subscriptionType = subData.subscription.userType;
                        if (subData.subscription.apiKey) {
                            accountData.openaiCredentials = {
                                apiKey: subData.subscription.apiKey,
                                baseURL: 'http://*************:7091/v1',
                                userType: subData.subscription.userType
                            };
                            this.log(`OpenAI密钥获取成功`, 'success', userInfo.name);
                        }
                    }
                } catch (e) {
                    this.log(`订阅信息解析失败`, 'error', userInfo.name);
                }
            }

            return accountData;

        } catch (error) {
            this.log(`获取API密钥异常: ${error.message}`, 'error', userInfo.name);
            return {
                id: userInfo.id,
                name: userInfo.name,
                email: userInfo.email,
                password: userInfo.password,
                registrationSuccess: false,
                loginSuccess: false,
                error: error.message
            };
        }
    }

    /**
     * 生成全随机密码
     */
    generateRandomPassword(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********!@#$%^&*';
        let password = '';

        // 确保包含至少一个大写字母、小写字母、数字和特殊字符
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
        password += '**********'[Math.floor(Math.random() * 10)];
        password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

        // 填充剩余长度
        for (let i = 4; i < length; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }

        // 打乱密码字符顺序
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    /**
     * 生成随机用户信息（使用全随机密码）
     */
    async generateRandomUser(accountIndex = 0, maxRetries = 5) {
        for (let retry = 0; retry < maxRetries; retry++) {
            const surname = this.surnames[Math.floor(Math.random() * this.surnames.length)];
            const givenName = this.givenNames[Math.floor(Math.random() * this.givenNames.length)];
            const fullName = surname + givenName;

            if (this.usedNames.has(fullName)) {
                continue;
            }

            const domainIndex = accountIndex % this.emailDomains.length;
            const selectedDomain = this.emailDomains[domainIndex];
            const emailInfo = await this.getTempEmail(selectedDomain);

            if (this.usedEmails.has(emailInfo.email)) {
                continue;
            }

            this.usedNames.add(fullName);
            this.usedEmails.add(emailInfo.email);

            // 生成全随机密码
            const password = this.generateRandomPassword(16);

            return {
                name: fullName,
                email: emailInfo.email,
                emailInfo: emailInfo,
                password: password,
                id: `user_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
                accountIndex: accountIndex,
                retry: retry,
                createdAt: new Date().toISOString()
            };
        }

        throw new Error(`经过${maxRetries}次尝试，无法生成唯一的用户信息`);
    }

    /**
     * 获取临时邮箱
     */
    async getTempEmail(preferredDomain = null) {
        try {
            const selectedDomain = preferredDomain || this.emailDomains[Math.floor(Math.random() * this.emailDomains.length)];

            const response = await this.request('https://www.guerrillamail.com/');
            const cookies = new Map();
            this.processCookies(response.cookies, cookies);

            if (response.statusCode === 200) {
                const emailMatches = response.body.match(/([a-zA-Z0-9+._-]{5,})@(sharklasers\.com|guerrillamail\.[a-z]+|grr\.la|pokemail\.net|spam4\.me)/g);

                if (emailMatches && emailMatches.length > 0) {
                    const currentEmail = emailMatches[0];
                    const [username, currentDomain] = currentEmail.split('@');

                    // 过滤系统邮箱
                    if (username.includes('no-reply') || username.includes('admin') || username.includes('support')) {
                        for (let i = 1; i < emailMatches.length; i++) {
                            const altEmail = emailMatches[i];
                            const [altUsername] = altEmail.split('@');
                            if (!altUsername.includes('no-reply') && !altUsername.includes('admin') && !altUsername.includes('support')) {
                                const finalEmail = selectedDomain !== currentDomain ? `${altUsername}@${selectedDomain}` : altEmail;
                                return {
                                    email: finalEmail,
                                    domain: selectedDomain,
                                    username: altUsername,
                                    cookies: cookies,
                                    sessionEstablished: true
                                };
                            }
                        }
                    } else {
                        const finalEmail = currentDomain !== selectedDomain ? `${username}@${selectedDomain}` : currentEmail;
                        return {
                            email: finalEmail,
                            domain: selectedDomain,
                            username: username,
                            cookies: cookies,
                            sessionEstablished: true
                        };
                    }
                }
            }
        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`获取临时邮箱失败: ${error.message}`, 'error');
            }
        }

        // 备选方案：生成随机邮箱
        const selectedDomain = preferredDomain || this.emailDomains[Math.floor(Math.random() * this.emailDomains.length)];
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substring(2, 8);
        const randomEmail = `${randomStr}${timestamp}@${selectedDomain}`;

        return {
            email: randomEmail,
            domain: selectedDomain,
            username: randomEmail.split('@')[0],
            cookies: new Map(),
            sessionEstablished: false
        };
    }

    /**
     * 注册账号
     */
    async registerAccount(userInfo) {
        try {
            const cookies = new Map();

            const homeResponse = await this.request('https://www.gankinterview.cn/');
            this.processCookies(homeResponse.cookies, cookies);

            const signupResponse = await this.request('https://www.gankinterview.cn/auth/signup', {
                cookies: this.cookiesToString(cookies)
            });
            this.processCookies(signupResponse.cookies, cookies);

            const registerData = JSON.stringify({
                name: userInfo.name,
                email: userInfo.email,
                password: userInfo.password,
                callbackUrl: '/app'
            });

            const registerResponse = await this.request('https://www.gankinterview.cn/api/auth/sign-up/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://www.gankinterview.cn/auth/signup',
                    'Origin': 'https://www.gankinterview.cn'
                },
                cookies: this.cookiesToString(cookies),
                body: registerData
            });

            if (registerResponse.statusCode === 200) {
                this.log(`注册成功`, 'success', userInfo.name);
                return { success: true, cookies: cookies, userInfo: userInfo };
            } else {
                const errorMsg = registerResponse.body.includes('already exists') ? '邮箱已存在' : `HTTP ${registerResponse.statusCode}`;
                this.log(`注册失败: ${errorMsg}`, 'error', userInfo.name);
                return { success: false, error: errorMsg };
            }

        } catch (error) {
            this.log(`注册异常: ${error.message}`, 'error', userInfo.name);
            return { success: false, error: error.message };
        }
    }

    /**
     * 检查邮箱验证邮件
     */
    async checkInbox(emailInfo, maxAttempts = 6) {
        let cookies = emailInfo.cookies || new Map();
        if (!emailInfo.sessionEstablished) {
            const guerrillaResponse = await this.request('https://www.guerrillamail.com/');
            cookies = new Map();
            this.processCookies(guerrillaResponse.cookies, cookies);
        }

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const inboxResponse = await this.request('https://www.guerrillamail.com/inbox', {
                    cookies: this.cookiesToString(cookies),
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                if (inboxResponse.statusCode === 200) {
                    const hasVerificationEmail = inboxResponse.body.includes('验证您的邮箱') ||
                                               inboxResponse.body.includes('验证') ||
                                               inboxResponse.body.includes('verify') ||
                                               inboxResponse.body.includes('useplunk') ||
                                               inboxResponse.body.includes('GankInterview');

                    if (hasVerificationEmail) {
                        const mailIdMatches = inboxResponse.body.match(/mail_id=(\d+)/g);
                        if (mailIdMatches && mailIdMatches.length > 0) {
                            const mailIds = mailIdMatches.map(match => parseInt(match.split('=')[1]));
                            const latestMailId = Math.max(...mailIds);

                            const verificationUrl = await this.getVerificationLink(latestMailId, cookies);
                            if (verificationUrl) {
                                return verificationUrl;
                            }
                        }
                    }
                }

                if (attempt < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 8000));
                }

            } catch (error) {
                if (this.options.logLevel === 'verbose') {
                    this.log(`检查邮箱异常: ${error.message}`, 'error');
                }
            }
        }

        return null;
    }

    /**
     * 获取验证链接
     */
    async getVerificationLink(mailId, cookies) {
        try {
            const emailResponse = await this.request(`https://www.guerrillamail.com/inbox?mail_id=${mailId}`, {
                cookies: this.cookiesToString(cookies),
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            });

            if (emailResponse.statusCode === 200) {
                const fullLinkMatch = emailResponse.body.match(/https:\/\/www\.gankinterview\.cn\/api\/auth\/verify-email\?token=([a-zA-Z0-9._-]+)&callbackURL=([^"'\s<>&]+)/);
                if (fullLinkMatch) {
                    return fullLinkMatch[0];
                }

                const tokenMatch = emailResponse.body.match(/token=([a-zA-Z0-9._-]{100,})/);
                if (tokenMatch) {
                    const token = tokenMatch[1];
                    return `https://www.gankinterview.cn/api/auth/verify-email?token=${token}&callbackURL=/app`;
                }

                const anyLinkMatch = emailResponse.body.match(/https:\/\/[^"'\s<>]*gankinterview[^"'\s<>]*/i);
                if (anyLinkMatch) {
                    return anyLinkMatch[0];
                }
            }

            return null;

        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`获取验证链接异常: ${error.message}`, 'error');
            }
            return null;
        }
    }

    /**
     * 执行邮箱验证
     */
    async verifyEmail(verificationUrl) {
        try {
            const verifyResponse = await this.request(verificationUrl);
            return verifyResponse.statusCode === 200 || verifyResponse.statusCode === 302;
        } catch (error) {
            if (this.options.logLevel === 'verbose') {
                this.log(`邮箱验证异常: ${error.message}`, 'error');
            }
            return false;
        }
    }

    /**
     * 创建单个账号（完整流程）
     */
    async createSingleAccount(accountIndex = 0) {
        try {
            const userInfo = await this.generateRandomUser(accountIndex);
            if (this.options.logLevel !== 'minimal') {
                this.log(`创建账号: ${userInfo.name} (${userInfo.email})`, 'info');
                this.log(`密码: ${userInfo.password}`, 'info', userInfo.name);
            }

            // 注册
            const registerResult = await this.registerAccount(userInfo);
            if (!registerResult.success) {
                return {
                    ...userInfo,
                    registrationSuccess: false,
                    error: registerResult.error
                };
            }

            // 等待并检查验证邮件
            const verificationUrl = await this.checkInbox(userInfo.emailInfo);
            if (!verificationUrl) {
                return {
                    ...userInfo,
                    registrationSuccess: true,
                    emailVerificationSuccess: false,
                    loginSuccess: false,
                    error: '未收到验证邮件'
                };
            }

            // 执行邮箱验证
            const verificationSuccess = await this.verifyEmail(verificationUrl);
            if (!verificationSuccess) {
                return {
                    ...userInfo,
                    registrationSuccess: true,
                    emailVerificationSuccess: false,
                    loginSuccess: false,
                    error: '邮箱验证失败'
                };
            }

            // 验证成功后登录
            const loginResult = await this.getOrCreateSession(userInfo);
            if (!loginResult.success) {
                return {
                    ...userInfo,
                    registrationSuccess: true,
                    emailVerificationSuccess: true,
                    loginSuccess: false,
                    error: loginResult.error
                };
            }

            // 获取API密钥
            const accountData = await this.getAccountCredentials(userInfo, loginResult);
            accountData.emailVerificationSuccess = true;

            return accountData;

        } catch (error) {
            this.log(`创建账号异常: ${error.message}`, 'error');
            return {
                registrationSuccess: false,
                emailVerificationSuccess: false,
                loginSuccess: false,
                error: error.message
            };
        }
    }

    /**
     * 批量创建账号
     */
    async createAccounts() {
        console.log(`🚀 开始创建 ${this.options.targetAccounts} 个账号\n`);

        for (let i = 0; i < this.options.targetAccounts; i++) {
            this.log(`创建第 ${i + 1}/${this.options.targetAccounts} 个账号`, 'info');

            try {
                const accountData = await this.createSingleAccount(i);
                this.results.accounts.push(accountData);

                if (accountData.loginSuccess) {
                    this.results.validAccounts++;
                    this.results.totalVoiceTime += accountData.voiceTime || 0;
                    this.log(`账号创建成功: ${accountData.name}`, 'success');
                } else {
                    this.results.errors.push(`${accountData.name || 'Unknown'}: ${accountData.error || '创建失败'}`);
                    this.log(`账号创建失败: ${accountData.error || '未知错误'}`, 'error');
                }

                if (i < this.options.targetAccounts - 1) {
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }

            } catch (error) {
                this.log(`批量创建异常: ${error.message}`, 'error');
                this.results.errors.push(`批量创建异常: ${error.message}`);
            }
        }
    }

    /**
     * 保存账号数据
     */
    saveAccounts() {
        try {
            const data = {
                accounts: this.results.accounts,
                summary: {
                    totalAccounts: this.results.accounts.length,
                    validAccounts: this.results.validAccounts,
                    totalVoiceMinutes: this.results.totalVoiceTime,
                    totalVoiceHours: Math.round(this.results.totalVoiceTime / 60 * 100) / 100,
                    lastUpdated: new Date().toISOString()
                },
                errors: this.results.errors
            };

            fs.writeFileSync(this.accountsFile, JSON.stringify(data, null, 2));
            this.log(`账号数据已保存: ${this.accountsFile}`, 'success');

        } catch (error) {
            this.log(`保存账号数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示总结
     */
    displaySummary() {
        console.log('\n' + '='.repeat(60));
        console.log('🎉 任务完成！');
        console.log('='.repeat(60));

        console.log(`\n📊 统计信息:`);
        console.log(`   总账号数: ${this.results.accounts.length}`);
        console.log(`   有效账号: ${this.results.validAccounts}`);
        console.log(`   成功率: ${Math.round(this.results.validAccounts / this.results.accounts.length * 100)}%`);

        console.log(`\n🎤 语音时长:`);
        console.log(`   总时长: ${this.results.totalVoiceTime} 分钟`);
        console.log(`   总时长: ${Math.round(this.results.totalVoiceTime / 60 * 100) / 100} 小时`);

        const azureCount = this.results.accounts.filter(acc => acc.azureCredentials).length;
        const openaiCount = this.results.accounts.filter(acc => acc.openaiCredentials).length;

        console.log(`\n🔑 API密钥:`);
        console.log(`   Azure令牌: ${azureCount}/${this.results.validAccounts}`);
        console.log(`   OpenAI密钥: ${openaiCount}/${this.results.validAccounts}`);

        if (this.results.validAccounts > 0) {
            console.log(`\n📋 有效账号:`);
            this.results.accounts.filter(acc => acc.loginSuccess).forEach((account, index) => {
                const voiceTime = `${account.voiceTime || 0}分钟`;
                const azure = account.azureCredentials ? '✅' : '❌';
                const openai = account.openaiCredentials ? '✅' : '❌';
                console.log(`   ${index + 1}. ${account.name}: ${voiceTime} | Azure:${azure} | OpenAI:${openai}`);
            });
        }

        console.log('\n' + '='.repeat(60));
    }

    /**
     * 主执行函数
     */
    async run(mode = 'create') {
        try {
            if (mode === 'create') {
                await this.createAccounts();
            } else if (mode === 'verify') {
                const accounts = this.loadExistingAccounts();
                if (accounts.length === 0) {
                    console.log('❌ 没有找到现有账号');
                    return;
                }
                await this.verifyAccounts(accounts);
            }

            this.saveSessions();
            this.saveAccounts();
            this.displaySummary();

        } catch (error) {
            this.log(`系统异常: ${error.message}`, 'error');
        }
    }
}

// 命令行接口
if (import.meta.url === `file://${process.argv[1]}`) {
    const args = process.argv.slice(2);
    const mode = args.find(arg => arg.startsWith('--mode='))?.split('=')[1] || 'create';
    const count = parseInt(args.find(arg => arg.startsWith('--count='))?.split('=')[1]) || 10;
    const logLevel = args.find(arg => arg.startsWith('--log='))?.split('=')[1] || 'normal';
    
    console.log('🎯 GankInterview 账号管理系统 - 最终版');
    console.log(`📋 模式: ${mode === 'create' ? '创建账号' : '验证账号'}`);
    if (mode === 'create') {
        console.log(`📊 目标: ${count} 个账号`);
    }
    console.log(`📝 日志级别: ${logLevel}\n`);
    
    const manager = new AccountManager({ 
        targetAccounts: count, 
        logLevel: logLevel 
    });
    
    manager.run(mode).catch(console.error);
}

export default AccountManager;
