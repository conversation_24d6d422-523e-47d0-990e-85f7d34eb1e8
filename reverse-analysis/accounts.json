{"accounts": [{"id": "manual_main_account", "name": "主账号", "email": "<EMAIL>", "password": "rampant.grouper", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2Vkeo5Wcpvj6sIf1FjaddBbpaR01ikGBdwtmivJEwoXOHuHJzENWnNfT9tjtiaEH9hyN8iKectizUYkMAN_HEg", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:31.494Z", "sessionFromCache": true}, {"id": "user_1753774741825_868", "name": "赵丽", "email": "<EMAIL>", "password": "zK$SOOwql9wyvuSx", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BL73HNvH5dvaT98036_9CEDI3qaQwiZlCP5ngbG_XwtAWQ-Mf6dog-qk3X8qPBy_9SITXfOTmnAwJdje5JX8FA", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:34.377Z", "sessionFromCache": true}, {"id": "user_1753774770654_805", "name": "马艳", "email": "<EMAIL>", "password": "6dcLo%71S72junTj", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AOKl73WSlMgSZC1iXd3TdJWDksWP6RXd5gLKcSh9aoe_OaDLLlxjQxRhcR-ribexZW3pS7ZBls5d9R-dqZa5_A", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:37.839Z", "sessionFromCache": true}, {"id": "user_1753774808322_665", "name": "马娟", "email": "<EMAIL>", "password": "gyWs^qe#oV@92uhH", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9iRKyTh65YpZeCwbJrsGeJVO9IDpIelSyAsDTYYPcu_G28GrP5AELUi1vBR6oY46hf-LuIkEBDqbq6190aeW_Q", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:40.659Z", "sessionFromCache": true}, {"id": "user_1753774836120_699", "name": "张军", "email": "<EMAIL>", "password": "1hN$l9giCl7i3NdD", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z1tg57sD9NCgyNhfxHtxDRYr6_HslXQIzBn4X9jwjor1vsli85Xu6v4IURlMZDvYKRUoksJAc052fCwLFHfTEg", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:43.484Z", "sessionFromCache": true}, {"id": "user_1753774872108_272", "name": "吴秀兰", "email": "<EMAIL>", "password": "oXwg8mFaZ2#pUkcL", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2WcW970xUNkzcxiLcxg0j-k4kFs5Z1HVlUMc2T2k9GuVUTUwKIGKa7BTttp1VQJNYk0hjJSmBfBKj-3OONmFvw", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:46.388Z", "sessionFromCache": true}, {"id": "user_1753774912777_753", "name": "朱磊", "email": "<EMAIL>", "password": "nk^ZFEvlN4sap3%J", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.79bsGsXUP8EzqD-0_JDuJ_IBp5VJn1Z7LyPv-WtXD2-V4uaWjFsFHO1GqQmmqoityMsfbSLN_0Dyk4KSvZFWwA", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:49.224Z", "sessionFromCache": true}, {"id": "user_1753774946798_29", "name": "徐思琪", "email": "<EMAIL>", "password": "@5ymkWUy@IpNi*2R", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yOXX24nqfeQ7KDy0P_nT0wZKtjd6QqLCacARW5jFLxuh-jmHtmQP93eq_goWGq7hJfCykdVjl-AE_2NEno7KGg", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:52.062Z", "sessionFromCache": true}, {"id": "user_1753774970705_997", "name": "林美丽", "email": "<EMAIL>", "password": "OYxyd0VRSm#SRUr&", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kOGzKjRP3MntnR-o5SbPf-AnZDfjb0c68IJvEmFNsrWYr1ZaZo_30xzhuAJs8BjOcide_BkA9GQWwLxbSe6VjA", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:54.934Z", "sessionFromCache": true}, {"id": "user_1753775000515_684", "name": "陈涛", "email": "<EMAIL>", "password": "UMmuwK44XBO$yX%B", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9-FlThzObshmFgE-O0JkDnTc6guhgdw4aLD4cATKC5PBjVFHC5OAOTKQcmmqO144TRTz-xfmSYPoB44nApIhzw", "region": "eastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:31:57.752Z", "sessionFromCache": true}, {"id": "user_1753775028028_689", "name": "李建华", "email": "<EMAIL>", "password": "9HTW!q!u18LufssW", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u6dK1jfhaNUaaEVXeWWehAxQn1zVOYTK62HV_hpQncCjt5r-G_df1hgpQB43VRCZ9fVeJdQGiNW189sLdkmcXA", "region": "southeastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:32:01.301Z", "sessionFromCache": true}, {"id": "user_1753775051607_867", "name": "孙强", "email": "<EMAIL>", "password": "!Vrynm!1$6daSO0S", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0K9cRYbBiSeWmr-woKbgWdT1Z9qGzk4bCQjMiFuSGgnaEH9BEI1nIy5QCI_gSNy1jNvr3hAy4mwPmFMmHVcpGw", "region": "southeastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:32:04.834Z", "sessionFromCache": true}, {"id": "user_1753775076051_429", "name": "孙浩然", "email": "<EMAIL>", "password": "PuhxZEou$!Y7VwBl", "registrationSuccess": true, "loginSuccess": true, "voiceTime": 30, "azureCredentials": {"token": "eyJhbGciOiJFUzI1NiIsImtpZCI6ImtleTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jtpQtml25SPwkMlJQF3kQBuLjBxyN9c3RUowA4u3MI59hwBqT8Cm6mNAmu62r8o0yNJoM-9C8F69cd5wfjea1g", "region": "southeastasia", "type": "jwt"}, "openaiCredentials": {"apiKey": "sk-bWqdExNFces6NnW5Ea29C76d5f7349C2A096B0164bB4Fc24", "baseURL": "http://*************:7091/v1", "userType": "TRIAL"}, "subscriptionType": "TRIAL", "createdAt": "2025-07-30T11:32:07.744Z", "sessionFromCache": true}], "summary": {"totalAccounts": 13, "validAccounts": 13, "totalVoiceMinutes": 390, "totalVoiceHours": 6.5, "lastUpdated": "2025-07-30T11:32:07.749Z"}, "errors": []}