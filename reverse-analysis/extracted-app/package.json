{"name": "gankinterview", "version": "1.2.0", "main": "dist/main/main.js", "author": "Minifold Technology Co., Ltd.", "license": "AGPL-3.0-or-later", "description": "An invisible desktop application to help you pass your all kinds of interviews.", "dependencies": {"@electron/notarize": "^2.3.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-toast": "^1.2.2", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.64.0", "axios": "^1.7.7", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "diff": "^7.0.0", "dotenv": "^16.4.7", "electron-log": "^5.2.4", "electron-store": "^10.0.0", "electron-updater": "^6.3.9", "form-data": "^4.0.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "json5": "^2.2.3", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "microsoft-cognitiveservices-speech-sdk": "^1.44.0", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "node-wav-player": "^1.0.0", "openai": "^4.28.4", "react": "^18.2.0", "react-code-blocks": "^0.1.6", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "screenshot-desktop": "^1.15.0", "tailwind-merge": "^2.5.5", "uiohook-napi": "^1.5.4", "utf-8-validate": "^5.0.10", "uuid": "^11.0.3", "zustand": "^5.0.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}